// ملف JavaScript لإعدادات النظام

document.addEventListener('DOMContentLoaded', function() {
    initializeSettings();
    loadSystemInfo();
    setupFormValidation();
});

// تهيئة صفحة الإعدادات
function initializeSettings() {
    // إضافة تأثيرات للأقسام
    animateSettingsSections();
    
    // إعداد معالجات الأحداث
    setupEventHandlers();
    
    // تحميل معلومات النظام
    loadDatabaseSize();
    loadLastBackupInfo();
}

// تحريك أقسام الإعدادات
function animateSettingsSections() {
    const sections = document.querySelectorAll('.settings-section');
    
    sections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            section.style.transition = 'all 0.6s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 200);
    });
}

// إعداد معالجات الأحداث
function setupEventHandlers() {
    // معالج نموذج الاستعادة
    const restoreForm = document.getElementById('restoreForm');
    if (restoreForm) {
        restoreForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitRestoreForm();
        });
    }
    
    // معالج تغيير ملف النسخة الاحتياطية
    const backupFile = document.getElementById('backup_file');
    if (backupFile) {
        backupFile.addEventListener('change', function() {
            validateBackupFile(this);
        });
    }
}

// إعداد التحقق من صحة النموذج
function setupFormValidation() {
    const form = document.querySelector('.settings-form');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            if (!validateSettingsForm()) {
                e.preventDefault();
                return false;
            }
            
            // إظهار مؤشر التحميل
            showLoadingOverlay('جاري حفظ الإعدادات...');
        });
    }
}

// التحقق من صحة نموذج الإعدادات
function validateSettingsForm() {
    const systemName = document.getElementById('system_name').value.trim();
    const centerName = document.getElementById('center_name').value.trim();
    
    if (!systemName) {
        alert('يرجى إدخال اسم النظام');
        document.getElementById('system_name').focus();
        return false;
    }
    
    if (!centerName) {
        alert('يرجى إدخال اسم المركز');
        document.getElementById('center_name').focus();
        return false;
    }
    
    return true;
}

// إنشاء نسخة احتياطية
function createBackup() {
    if (confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')) {
        showLoadingOverlay('جاري إنشاء النسخة الاحتياطية...');
        
        fetch('create_backup.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: 'create_backup' })
        })
        .then(response => response.blob())
        .then(blob => {
            hideLoadingOverlay();
            
            // تحميل الملف
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `backup_${new Date().toISOString().split('T')[0]}.sql`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showSuccessMessage('تم إنشاء النسخة الاحتياطية بنجاح');
            loadLastBackupInfo();
        })
        .catch(error => {
            hideLoadingOverlay();
            console.error('خطأ:', error);
            alert('حدث خطأ في إنشاء النسخة الاحتياطية');
        });
    }
}

// استعادة نسخة احتياطية
function restoreBackup() {
    document.getElementById('restoreModal').classList.add('show');
}

// إغلاق نافذة الاستعادة
function closeRestoreModal() {
    document.getElementById('restoreModal').classList.remove('show');
    document.getElementById('restoreForm').reset();
}

// التحقق من صحة ملف النسخة الاحتياطية
function validateBackupFile(input) {
    const file = input.files[0];
    if (!file) return;
    
    const allowedTypes = ['.sql', '.zip'];
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    
    if (!allowedTypes.includes(fileExtension)) {
        alert('يرجى اختيار ملف بصيغة SQL أو ZIP');
        input.value = '';
        return false;
    }
    
    // التحقق من حجم الملف (أقل من 100 ميجابايت)
    if (file.size > 100 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 100 ميجابايت');
        input.value = '';
        return false;
    }
    
    return true;
}

// إرسال نموذج الاستعادة
function submitRestoreForm() {
    const fileInput = document.getElementById('backup_file');
    const confirmCheckbox = document.getElementById('confirm_restore');
    
    if (!fileInput.files[0]) {
        alert('يرجى اختيار ملف النسخة الاحتياطية');
        return;
    }
    
    if (!confirmCheckbox.checked) {
        alert('يرجى تأكيد عملية الاستعادة');
        return;
    }
    
    if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟\nسيتم حذف جميع البيانات الحالية!')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('backup_file', fileInput.files[0]);
    
    showLoadingOverlay('جاري استعادة النسخة الاحتياطية...');
    
    fetch('restore_backup.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingOverlay();
        
        if (data.success) {
            closeRestoreModal();
            showSuccessMessage('تم استعادة النسخة الاحتياطية بنجاح');
            
            // إعادة تحميل الصفحة بعد 3 ثوان
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        hideLoadingOverlay();
        console.error('خطأ:', error);
        alert('حدث خطأ في استعادة النسخة الاحتياطية');
    });
}

// تحسين قاعدة البيانات
function optimizeDatabase() {
    if (confirm('هل تريد تحسين قاعدة البيانات؟\nقد تستغرق هذه العملية بعض الوقت.')) {
        showLoadingOverlay('جاري تحسين قاعدة البيانات...');
        
        fetch('optimize_database.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: 'optimize' })
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingOverlay();
            
            if (data.success) {
                showSuccessMessage('تم تحسين قاعدة البيانات بنجاح');
                loadDatabaseSize();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            hideLoadingOverlay();
            console.error('خطأ:', error);
            alert('حدث خطأ في تحسين قاعدة البيانات');
        });
    }
}

// مسح السجلات القديمة
function clearOldLogs() {
    const days = prompt('كم يوماً تريد الاحتفاظ بالسجلات؟ (افتراضي: 30)', '30');
    
    if (days === null) return;
    
    const daysNumber = parseInt(days);
    if (isNaN(daysNumber) || daysNumber < 1) {
        alert('يرجى إدخال رقم صحيح أكبر من 0');
        return;
    }
    
    if (confirm(`هل تريد حذف السجلات الأقدم من ${daysNumber} يوم؟`)) {
        showLoadingOverlay('جاري مسح السجلات القديمة...');
        
        fetch('clear_old_logs.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ days: daysNumber })
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingOverlay();
            
            if (data.success) {
                showSuccessMessage(`تم حذف ${data.deleted_count} سجل قديم`);
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            hideLoadingOverlay();
            console.error('خطأ:', error);
            alert('حدث خطأ في مسح السجلات');
        });
    }
}

// إعادة تعيين النظام
function resetSystem() {
    const confirmation = prompt('لإعادة تعيين النظام، اكتب "RESET" بالأحرف الكبيرة:', '');
    
    if (confirmation !== 'RESET') {
        if (confirmation !== null) {
            alert('تم إلغاء العملية');
        }
        return;
    }
    
    if (confirm('هل أنت متأكد من إعادة تعيين النظام؟\nسيتم حذف جميع البيانات نهائياً!')) {
        showLoadingOverlay('جاري إعادة تعيين النظام...');
        
        fetch('reset_system.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: 'reset', confirmation: 'RESET' })
        })
        .then(response => response.json())
        .then(data => {
            hideLoadingOverlay();
            
            if (data.success) {
                alert('تم إعادة تعيين النظام بنجاح');
                window.location.href = '../login/login.php';
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            hideLoadingOverlay();
            console.error('خطأ:', error);
            alert('حدث خطأ في إعادة تعيين النظام');
        });
    }
}

// تحميل حجم قاعدة البيانات
function loadDatabaseSize() {
    fetch('get_database_size.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('databaseSize').textContent = data.size;
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل حجم قاعدة البيانات:', error);
        });
}

// تحميل معلومات آخر نسخة احتياطية
function loadLastBackupInfo() {
    fetch('get_last_backup.php')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.last_backup) {
                document.getElementById('lastBackup').textContent = data.last_backup;
            } else {
                document.getElementById('lastBackup').textContent = 'لم يتم إنشاء نسخة احتياطية بعد';
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل معلومات النسخة الاحتياطية:', error);
        });
}

// تحميل معلومات النظام
function loadSystemInfo() {
    // يمكن إضافة المزيد من معلومات النظام هنا
    console.log('تم تحميل معلومات النظام');
}

// إظهار شاشة التحميل
function showLoadingOverlay(message) {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>${message}</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

// إخفاء شاشة التحميل
function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success';
    successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.5s ease-out;
    `;
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
        successDiv.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => successDiv.remove(), 500);
    }, 4000);
}

// إغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    const restoreModal = document.getElementById('restoreModal');
    
    if (e.target === restoreModal) {
        closeRestoreModal();
    }
});

// إغلاق النوافذ بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeRestoreModal();
    }
});

// إضافة تأثيرات CSS للحركة
const style = document.createElement('style');
style.textContent = `
@keyframes slideInRight {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideOutRight {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(100%); }
}
`;
document.head.appendChild(style);
