<?php
require_once '../config/init.php';
checkLogin();

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(part_name LIKE ? OR barcode LIKE ? OR notes LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد القطع
$count_sql = "SELECT COUNT(*) as total FROM spare_parts $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_parts = $count_stmt->fetch()['total'];
$total_pages = ceil($total_parts / $per_page);

// الحصول على قطع الغيار
$sql = "SELECT * FROM spare_parts $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$parts = $stmt->fetchAll();

// حساب إحصائيات سريعة
$stats_sql = "SELECT 
    COUNT(*) as total_parts,
    SUM(quantity) as total_quantity,
    SUM(quantity * purchase_price) as total_purchase_value,
    SUM(quantity * selling_price) as total_selling_value
    FROM spare_parts";
$stats_stmt = $conn->prepare($stats_sql);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - إدارة قطع الغيار</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="../repair/repair.css">
    <link rel="stylesheet" href="parts.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="../repair/add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="../repair/view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="parts.php" class="nav-item active">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-cogs"></i> إدارة قطع الغيار</h1>
                <p>إدارة مخزون قطع الغيار والمعدات</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openAddPartModal()">
                    <i class="fas fa-plus"></i> إضافة قطعة جديدة
                </button>
                <button class="btn btn-success" onclick="openImportModal()">
                    <i class="fas fa-file-excel"></i> استيراد Excel
                </button>
                <button class="btn btn-secondary" onclick="exportParts()">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </header>

        <!-- أدوات البحث -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="البحث في قطع الغيار..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="parts.php" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card parts">
                <div class="stat-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['total_parts']); ?></h3>
                    <p>إجمالي القطع</p>
                </div>
            </div>
            
            <div class="stat-card quantity">
                <div class="stat-icon">
                    <i class="fas fa-boxes"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['total_quantity']); ?></h3>
                    <p>إجمالي الكمية</p>
                </div>
            </div>
            
            <div class="stat-card purchase">
                <div class="stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo formatCurrency($stats['total_purchase_value']); ?></h3>
                    <p>قيمة الشراء</p>
                </div>
            </div>
            
            <div class="stat-card selling">
                <div class="stat-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo formatCurrency($stats['total_selling_value']); ?></h3>
                    <p>قيمة البيع</p>
                </div>
            </div>
        </div>

        <!-- جدول قطع الغيار -->
        <div class="table-container">
            <?php if (empty($parts)): ?>
            <div class="no-data">
                <i class="fas fa-cogs"></i>
                <h3>لا توجد قطع غيار</h3>
                <p>لم يتم العثور على أي قطع غيار تطابق معايير البحث</p>
                <button class="btn btn-primary" onclick="openAddPartModal()">
                    <i class="fas fa-plus"></i> إضافة قطعة جديدة
                </button>
            </div>
            <?php else: ?>
            <table class="parts-table">
                <thead>
                    <tr>
                        <th>اسم القطعة</th>
                        <th>الباركود</th>
                        <th>سعر الشراء</th>
                        <th>سعر البيع</th>
                        <th>الكمية</th>
                        <th>الربح</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($parts as $part): ?>
                    <?php 
                    $profit = $part['selling_price'] - $part['purchase_price'];
                    $profit_percentage = $part['purchase_price'] > 0 ? ($profit / $part['purchase_price']) * 100 : 0;
                    $stock_status = $part['quantity'] <= 5 ? 'low' : ($part['quantity'] <= 10 ? 'medium' : 'high');
                    ?>
                    <tr>
                        <td>
                            <strong><?php echo htmlspecialchars($part['part_name']); ?></strong>
                            <?php if ($part['notes']): ?>
                            <br><small style="color: #7f8c8d;"><?php echo htmlspecialchars(substr($part['notes'], 0, 50)); ?>...</small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <code class="barcode"><?php echo htmlspecialchars($part['barcode']); ?></code>
                        </td>
                        <td>
                            <span class="price purchase-price"><?php echo formatCurrency($part['purchase_price']); ?></span>
                        </td>
                        <td>
                            <span class="price selling-price"><?php echo formatCurrency($part['selling_price']); ?></span>
                        </td>
                        <td>
                            <span class="quantity quantity-<?php echo $stock_status; ?>">
                                <?php echo number_format($part['quantity']); ?>
                            </span>
                        </td>
                        <td>
                            <span class="profit <?php echo $profit >= 0 ? 'positive' : 'negative'; ?>">
                                <?php echo formatCurrency($profit); ?>
                                <small>(<?php echo number_format($profit_percentage, 1); ?>%)</small>
                            </span>
                        </td>
                        <td>
                            <span class="stock-status status-<?php echo $stock_status; ?>">
                                <?php 
                                switch($stock_status) {
                                    case 'low': echo 'مخزون منخفض'; break;
                                    case 'medium': echo 'مخزون متوسط'; break;
                                    case 'high': echo 'متوفر'; break;
                                }
                                ?>
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view" onclick="viewPart(<?php echo $part['id']; ?>)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit" onclick="editPart(<?php echo $part['id']; ?>)" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-print" onclick="printPartLabel(<?php echo $part['id']; ?>)" title="طباعة ملصق">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn-action btn-stock" onclick="updateStock(<?php echo $part['id']; ?>)" title="تحديث المخزون">
                                    <i class="fas fa-boxes"></i>
                                </button>
                                <button class="btn-action btn-delete" onclick="deletePart(<?php echo $part['id']; ?>)" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- ترقيم الصفحات -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>" class="page-btn">
                    <i class="fas fa-chevron-right"></i> السابق
                </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>" 
                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                    <?php echo $i; ?>
                </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>" class="page-btn">
                    التالي <i class="fas fa-chevron-left"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل قطعة -->
    <div id="partModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="partModalTitle"><i class="fas fa-plus"></i> إضافة قطعة غيار جديدة</h3>
                <button class="modal-close" onclick="closePartModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="partForm">
                <div class="modal-body">
                    <input type="hidden" id="part_id">
                    
                    <div class="form-group">
                        <label for="part_name">اسم القطعة *</label>
                        <input type="text" id="part_name" required placeholder="أدخل اسم القطعة">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="purchase_price">سعر الشراء *</label>
                            <input type="number" id="purchase_price" step="0.01" min="0" required placeholder="0.00">
                        </div>
                        
                        <div class="form-group">
                            <label for="selling_price">سعر البيع *</label>
                            <input type="number" id="selling_price" step="0.01" min="0" required placeholder="0.00">
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="quantity">الكمية *</label>
                            <input type="number" id="quantity" min="0" required placeholder="0">
                        </div>
                        
                        <div class="form-group">
                            <label for="part_barcode">الباركود</label>
                            <input type="text" id="part_barcode" placeholder="سيتم إنشاؤه تلقائياً">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="part_notes">ملاحظات</label>
                        <textarea id="part_notes" placeholder="أي ملاحظات إضافية"></textarea>
                    </div>
                    
                    <div class="profit-preview" id="profitPreview" style="display: none;">
                        <div class="profit-info">
                            <span>الربح المتوقع: </span>
                            <span id="profitAmount" class="profit-amount"></span>
                            <span id="profitPercentage" class="profit-percentage"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ القطعة
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closePartModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تحديث المخزون -->
    <div id="stockModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-boxes"></i> تحديث مخزون القطعة</h3>
                <button class="modal-close" onclick="closeStockModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="stockForm">
                <div class="modal-body">
                    <input type="hidden" id="stock_part_id">
                    
                    <div class="stock-info">
                        <h4 id="stock_part_name"></h4>
                        <p>الكمية الحالية: <span id="current_quantity" class="current-stock"></span></p>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock_operation">نوع العملية</label>
                        <select id="stock_operation" required onchange="toggleStockFields()">
                            <option value="">اختر نوع العملية</option>
                            <option value="add">إضافة للمخزون</option>
                            <option value="subtract">خصم من المخزون</option>
                            <option value="set">تحديد كمية جديدة</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="stock_quantity">الكمية</label>
                        <input type="number" id="stock_quantity" min="0" required placeholder="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="stock_notes">سبب التحديث</label>
                        <textarea id="stock_notes" placeholder="اكتب سبب تحديث المخزون"></textarea>
                    </div>
                    
                    <div class="new-quantity-preview" id="newQuantityPreview" style="display: none;">
                        <p>الكمية الجديدة ستكون: <span id="newQuantityValue" class="new-quantity"></span></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> تحديث المخزون
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeStockModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة استيراد Excel -->
    <div id="importModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-file-excel"></i> استيراد قطع الغيار من Excel</h3>
                <button class="modal-close" onclick="closeImportModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="importForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="import-instructions">
                        <h4>تعليمات الاستيراد:</h4>
                        <ul>
                            <li>يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)</li>
                            <li>الصف الأول يجب أن يحتوي على العناوين</li>
                            <li>الأعمدة المطلوبة: اسم القطعة، سعر الشراء، سعر البيع، الكمية</li>
                            <li>الأعمدة الاختيارية: الباركود، الملاحظات</li>
                        </ul>
                    </div>
                    
                    <div class="form-group">
                        <label for="excel_file">اختر ملف Excel</label>
                        <input type="file" id="excel_file" accept=".xlsx,.xls" required>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="update_existing"> 
                            تحديث القطع الموجودة (بناءً على الباركود)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i> استيراد البيانات
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="downloadTemplate()">
                        <i class="fas fa-download"></i> تحميل نموذج
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeImportModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="parts.js"></script>
</body>
</html>
