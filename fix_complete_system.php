<?php
// ملف إصلاح شامل للنظام بالكامل

// إعداد الترميز
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
mb_regex_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح شامل للنظام</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; text-align: right; padding: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #e74c3c; background: #ffebee; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #3498db; background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #f39c12; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo "h1 { color: #2c3e50; text-align: center; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo ".btn { background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }";
echo ".btn-success { background: #27ae60; }";
echo ".btn-danger { background: #e74c3c; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح شامل للنظام</h1>";

try {
    require_once 'config/database.php';
    
    $database = new Database();
    $conn = $database->connect();
    
    if (!$conn) {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    
    echo "<div class='info'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // الخطوة 1: إعادة إنشاء جدول الإعدادات بالشكل الصحيح
    echo "<div class='step'>";
    echo "<h2>الخطوة 1: إعادة إنشاء جدول الإعدادات</h2>";
    
    $conn->exec("DROP TABLE IF EXISTS settings");
    $create_settings = "
    CREATE TABLE settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) NOT NULL UNIQUE,
        setting_value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_settings);
    echo "<div class='success'>✅ تم إعادة إنشاء جدول الإعدادات</div>";
    
    // إدراج الإعدادات الافتراضية
    $settings = [
        'system_name' => 'نظام إدارة صيانة الموبايلات',
        'center_name' => 'مركز صيانة الموبايلات',
        'center_address' => 'العراق - بغداد',
        'center_phone' => '07xxxxxxxxx',
        'center_social' => 'Facebook: @center | Instagram: @center',
        'currency' => 'IQD',
        'print_size' => 'A5',
        'backup_frequency' => 'weekly',
        'auto_backup' => '1',
        'email_notifications' => '1',
        'sms_notifications' => '0'
    ];
    
    foreach ($settings as $key => $value) {
        $sql = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$key, $value]);
    }
    echo "<div class='success'>✅ تم إدراج الإعدادات الافتراضية</div>";
    echo "</div>";
    
    // الخطوة 2: إعادة إنشاء جدول المستخدمين
    echo "<div class='step'>";
    echo "<h2>الخطوة 2: إعادة إنشاء جدول المستخدمين</h2>";
    
    $conn->exec("DROP TABLE IF EXISTS users");
    $create_users = "
    CREATE TABLE users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        email VARCHAR(100),
        phone VARCHAR(20),
        role ENUM('admin', 'user') DEFAULT 'user',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_users);
    echo "<div class='success'>✅ تم إعادة إنشاء جدول المستخدمين</div>";
    
    // إدراج المستخدم الافتراضي
    $password_hash = password_hash('ZAin1998', PASSWORD_DEFAULT);
    $sql = "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute(['abd', $password_hash, 'المدير العام', 'admin']);
    echo "<div class='success'>✅ تم إنشاء المستخدم الافتراضي (abd)</div>";
    echo "</div>";
    
    // الخطوة 3: إعادة إنشاء جدول سجل العمليات
    echo "<div class='step'>";
    echo "<h2>الخطوة 3: إعادة إنشاء جدول سجل العمليات</h2>";
    
    $conn->exec("DROP TABLE IF EXISTS activity_logs");
    $create_activity_logs = "
    CREATE TABLE activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        action_type VARCHAR(100) NOT NULL,
        table_name VARCHAR(50) NOT NULL,
        record_id INT NULL,
        old_data JSON NULL,
        new_data JSON NULL,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_action_type (action_type),
        INDEX idx_table_name (table_name),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_activity_logs);
    echo "<div class='success'>✅ تم إعادة إنشاء جدول سجل العمليات</div>";
    echo "</div>";
    
    // الخطوة 4: إعادة إنشاء جدول التسلسل
    echo "<div class='step'>";
    echo "<h2>الخطوة 4: إعادة إنشاء جدول التسلسل</h2>";
    
    $conn->exec("DROP TABLE IF EXISTS sequences");
    $create_sequences = "
    CREATE TABLE sequences (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sequence_name VARCHAR(50) NOT NULL UNIQUE,
        current_value INT NOT NULL DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_sequences);
    echo "<div class='success'>✅ تم إعادة إنشاء جدول التسلسل</div>";
    
    // إدراج التسلسلات الافتراضية
    $sequences = [
        ['order', 0],
        ['receipt', 0], 
        ['barcode', 0]
    ];
    
    foreach ($sequences as $seq) {
        $sql = "INSERT INTO sequences (sequence_name, current_value) VALUES (?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute($seq);
    }
    echo "<div class='success'>✅ تم إدراج التسلسلات الافتراضية</div>";
    echo "</div>";
    
    // الخطوة 5: إصلاح جدول العملاء
    echo "<div class='step'>";
    echo "<h2>الخطوة 5: إصلاح جدول العملاء</h2>";
    
    $alter_customers = [
        "ALTER TABLE customers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY full_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY phone VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY address TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY social_media VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY notes TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    foreach ($alter_customers as $sql) {
        try {
            $conn->exec($sql);
            echo "<div class='success'>✅ تم إصلاح جدول العملاء</div>";
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ تحذير: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 6: إصلاح جدول طلبات الصيانة
    echo "<div class='step'>";
    echo "<h2>الخطوة 6: إصلاح جدول طلبات الصيانة</h2>";
    
    $alter_orders = [
        "ALTER TABLE repair_orders CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY order_number VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY phone_model VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY problem_description TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY notes TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    foreach ($alter_orders as $sql) {
        try {
            $conn->exec($sql);
            echo "<div class='success'>✅ تم إصلاح جدول طلبات الصيانة</div>";
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ تحذير: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 7: إصلاح جدول قطع الغيار
    echo "<div class='step'>";
    echo "<h2>الخطوة 7: إصلاح جدول قطع الغيار</h2>";
    
    $alter_parts = [
        "ALTER TABLE spare_parts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE spare_parts MODIFY part_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE spare_parts MODIFY notes TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    foreach ($alter_parts as $sql) {
        try {
            $conn->exec($sql);
            echo "<div class='success'>✅ تم إصلاح جدول قطع الغيار</div>";
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ تحذير: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 8: إعادة إدراج العلامات التجارية
    echo "<div class='step'>";
    echo "<h2>الخطوة 8: إعادة إدراج العلامات التجارية</h2>";
    
    $conn->exec("ALTER TABLE phone_brands CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    $conn->exec("DELETE FROM phone_brands");
    
    $brands = [
        'سامسونج (Samsung)',
        'آيفون (Apple)', 
        'هواوي (Huawei)',
        'شاومي (Xiaomi)',
        'أوبو (Oppo)',
        'فيفو (Vivo)',
        'ون بلس (OnePlus)',
        'نوكيا (Nokia)',
        'إل جي (LG)',
        'سوني (Sony)',
        'ريلمي (Realme)',
        'تكنو (Tecno)',
        'إنفينكس (Infinix)',
        'أخرى'
    ];
    
    foreach ($brands as $brand) {
        $sql = "INSERT INTO phone_brands (brand_name) VALUES (?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$brand]);
    }
    echo "<div class='success'>✅ تم إعادة إدراج " . count($brands) . " علامة تجارية بالعربية</div>";
    echo "</div>";
    
    // الخطوة 9: اختبار شامل للنظام
    echo "<div class='step'>";
    echo "<h2>الخطوة 9: اختبار شامل للنظام</h2>";
    
    // اختبار إضافة عميل
    try {
        $test_customer = "عميل اختبار النظام المُحدث";
        $test_phone = "07700000001";
        $test_address = "عنوان اختبار النظام المُحدث";
        
        $sql = "INSERT INTO customers (full_name, phone, address, total_debt) VALUES (?, ?, ?, 0.00)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$test_customer, $test_phone, $test_address]);
        
        $customer_id = $conn->lastInsertId();
        echo "<div class='success'>✅ اختبار إضافة العملاء ناجح - ID: $customer_id</div>";
        
        // حذف العميل التجريبي
        $sql = "DELETE FROM customers WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$customer_id]);
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في اختبار العملاء: " . $e->getMessage() . "</div>";
    }
    
    // اختبار إضافة قطعة غيار
    try {
        $test_part = "قطعة غيار اختبار النظام المُحدث";
        $sql = "INSERT INTO spare_parts (part_name, purchase_price, selling_price, quantity) VALUES (?, 100, 150, 5)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$test_part]);
        
        $part_id = $conn->lastInsertId();
        echo "<div class='success'>✅ اختبار إضافة قطع الغيار ناجح - ID: $part_id</div>";
        
        // حذف القطعة التجريبية
        $sql = "DELETE FROM spare_parts WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$part_id]);
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في اختبار قطع الغيار: " . $e->getMessage() . "</div>";
    }
    
    // اختبار دوال قاعدة البيانات
    try {
        $next_number = $database->getNextSequenceNumber('order');
        echo "<div class='success'>✅ اختبار دالة getNextSequenceNumber ناجح - الرقم: $next_number</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في دالة getNextSequenceNumber: " . $e->getMessage() . "</div>";
    }
    
    try {
        $database->logActivity('اختبار النظام المُحدث', 'test', 1, null, ['test' => 'data'], 'اختبار تسجيل العمليات');
        echo "<div class='success'>✅ اختبار دالة logActivity ناجح</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في دالة logActivity: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🎉 تم إصلاح النظام بالكامل بنجاح!</h2>";
    echo "<div class='success'>";
    echo "<h3>الإصلاحات المُنجزة:</h3>";
    echo "<ul>";
    echo "<li>✅ إعادة إنشاء جدول الإعدادات بالشكل الصحيح</li>";
    echo "<li>✅ إعادة إنشاء جدول المستخدمين</li>";
    echo "<li>✅ إعادة إنشاء جدول سجل العمليات</li>";
    echo "<li>✅ إعادة إنشاء جدول التسلسل</li>";
    echo "<li>✅ إصلاح ترميز جميع الجداول</li>";
    echo "<li>✅ إعادة إدراج العلامات التجارية بالعربية</li>";
    echo "<li>✅ اختبار جميع وظائف النظام</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>الآن النظام جاهز تماماً:</h3>";
    echo "<ul>";
    echo "<li>✅ تسجيل الدخول: abd / ZAin1998</li>";
    echo "<li>✅ إضافة عملاء بأسماء عربية</li>";
    echo "<li>✅ إضافة قطع غيار بأسماء عربية</li>";
    echo "<li>✅ إضافة طلبات صيانة</li>";
    echo "<li>✅ جميع النصوص تظهر بشكل صحيح</li>";
    echo "<li>✅ لا توجد علامات استفهام</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ حدث خطأ عام: " . $e->getMessage() . "</div>";
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='index.php' class='btn btn-success'>العودة للنظام</a>";
echo "<a href='dashboard/dashboard.php' class='btn'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
