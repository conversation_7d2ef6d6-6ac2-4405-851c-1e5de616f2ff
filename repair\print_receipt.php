<?php
require_once '../config/init.php';
checkLogin();

$order_id = (int)$_GET['id'];

// الحصول على تفاصيل الطلب
$sql = "SELECT r.*, c.full_name, c.phone, c.address, b.brand_name 
        FROM repair_orders r 
        JOIN customers c ON r.customer_id = c.id 
        JOIN phone_brands b ON r.phone_brand_id = b.id 
        WHERE r.id = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$order_id]);
$order = $stmt->fetch();

if (!$order) {
    die('الطلب غير موجود');
}

// تحديد قياس الطباعة
$print_size = getSetting('print_size', 'A5');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إيصال صيانة - <?php echo $order['order_number']; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: white;
            color: #000;
            line-height: 1.6;
        }

        .receipt-container {
            <?php if ($print_size === 'A5'): ?>
            width: 148mm;
            min-height: 210mm;
            <?php elseif ($print_size === 'thermal'): ?>
            width: 48mm;
            min-height: auto;
            <?php else: ?>
            width: 210mm;
            min-height: 297mm;
            <?php endif; ?>
            margin: 0 auto;
            padding: 10mm;
            background: white;
            border: 1px solid #ddd;
        }

        .receipt-header {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .center-logo {
            width: 60px;
            height: 60px;
            background: #3498db;
            border-radius: 50%;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .center-name {
            font-size: 18px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .center-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 3px;
        }

        .receipt-title {
            background: #3498db;
            color: white;
            padding: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: 600;
            font-size: 16px;
        }

        .order-number {
            background: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: 700;
            font-size: 14px;
            display: inline-block;
            margin-bottom: 10px;
        }

        .receipt-details {
            margin-bottom: 15px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px dotted #ccc;
        }

        .detail-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .detail-value {
            color: #000;
        }

        .customer-section {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
        }

        .section-title {
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 14px;
            border-bottom: 1px solid #3498db;
            padding-bottom: 3px;
        }

        .problem-description {
            background: #fff3cd;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ffeaa7;
            margin-bottom: 15px;
        }

        .cost-section {
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
            margin-bottom: 15px;
        }

        .cost-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .total-cost {
            font-size: 16px;
            font-weight: 700;
            color: #27ae60;
            border-top: 2px solid #27ae60;
            padding-top: 5px;
            margin-top: 8px;
        }

        .barcode-section {
            text-align: center;
            margin: 15px 0;
            padding: 10px;
            border: 1px dashed #666;
        }

        .barcode {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            font-weight: 700;
            letter-spacing: 2px;
            margin-bottom: 5px;
        }

        .receipt-footer {
            text-align: center;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 2px solid #000;
            font-size: 12px;
        }

        .thank-you {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            color: #27ae60;
            font-weight: 600;
        }

        .print-buttons {
            text-align: center;
            margin: 20px 0;
        }

        .print-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            margin: 0 5px;
        }

        .print-btn:hover {
            background: #2980b9;
        }

        /* إخفاء الأزرار عند الطباعة */
        @media print {
            .print-buttons {
                display: none !important;
            }
            
            .receipt-container {
                border: none !important;
                margin: 0 !important;
                padding: 5mm !important;
            }
            
            body {
                background: white !important;
            }
        }

        /* تنسيق للطابعة الحرارية */
        <?php if ($print_size === 'thermal'): ?>
        .receipt-container {
            font-size: 10px;
            padding: 2mm;
        }
        
        .center-logo {
            width: 30px;
            height: 30px;
            font-size: 12px;
        }
        
        .center-name {
            font-size: 12px;
        }
        
        .receipt-title {
            font-size: 11px;
            padding: 4px;
        }
        
        .detail-row {
            font-size: 9px;
        }
        <?php endif; ?>
    </style>
</head>
<body>
    <div class="print-buttons">
        <button class="print-btn" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة الإيصال
        </button>
        <button class="print-btn" onclick="window.close()" style="background: #95a5a6;">
            إغلاق
        </button>
    </div>

    <div class="receipt-container">
        <!-- رأس الإيصال -->
        <div class="receipt-header">
            <div class="center-logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <div class="center-name"><?php echo $system_settings['center_name']; ?></div>
            <div class="center-info"><?php echo $system_settings['center_address']; ?></div>
            <div class="center-info">هاتف: <?php echo $system_settings['center_phone']; ?></div>
            <div class="center-info"><?php echo $system_settings['center_social']; ?></div>
        </div>

        <!-- عنوان الإيصال -->
        <div class="receipt-title">إيصال استلام جهاز للصيانة</div>

        <!-- رقم الطلب -->
        <div style="text-align: center;">
            <span class="order-number">رقم الطلب: <?php echo $order['order_number']; ?></span>
        </div>

        <!-- معلومات العميل -->
        <div class="customer-section">
            <div class="section-title">معلومات العميل</div>
            <div class="detail-row">
                <span class="detail-label">الاسم:</span>
                <span class="detail-value"><?php echo htmlspecialchars($order['full_name']); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الهاتف:</span>
                <span class="detail-value"><?php echo htmlspecialchars($order['phone']); ?></span>
            </div>
            <?php if ($order['address']): ?>
            <div class="detail-row">
                <span class="detail-label">العنوان:</span>
                <span class="detail-value"><?php echo htmlspecialchars($order['address']); ?></span>
            </div>
            <?php endif; ?>
        </div>

        <!-- تفاصيل الجهاز -->
        <div class="receipt-details">
            <div class="section-title">تفاصيل الجهاز</div>
            <div class="detail-row">
                <span class="detail-label">نوع الجهاز:</span>
                <span class="detail-value"><?php echo htmlspecialchars($order['brand_name']); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الموديل:</span>
                <span class="detail-value"><?php echo htmlspecialchars($order['phone_model']); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">تاريخ الاستلام:</span>
                <span class="detail-value"><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></span>
            </div>
        </div>

        <!-- وصف المشكلة -->
        <div class="problem-description">
            <div class="section-title">وصف المشكلة</div>
            <p><?php echo nl2br(htmlspecialchars($order['problem_description'])); ?></p>
        </div>

        <!-- التكلفة والدفع -->
        <div class="cost-section">
            <div class="section-title">تفاصيل التكلفة</div>
            <div class="cost-row">
                <span>تكلفة الصيانة:</span>
                <span><?php echo formatCurrency($order['repair_cost']); ?></span>
            </div>
            <div class="cost-row">
                <span>المبلغ المدفوع:</span>
                <span><?php echo formatCurrency($order['paid_amount']); ?></span>
            </div>
            <div class="cost-row">
                <span>المبلغ المتبقي:</span>
                <span><?php echo formatCurrency($order['remaining_amount']); ?></span>
            </div>
            <div class="cost-row total-cost">
                <span>طريقة الدفع:</span>
                <span>
                    <?php
                    switch($order['payment_method']) {
                        case 'cash': echo 'نقداً (كامل)'; break;
                        case 'partial': echo 'دفع جزئي'; break;
                        case 'after_repair': echo 'آجل بعد الصيانة'; break;
                    }
                    ?>
                </span>
            </div>
        </div>

        <!-- الملاحظات -->
        <?php if ($order['notes']): ?>
        <div class="receipt-details">
            <div class="section-title">ملاحظات</div>
            <p><?php echo nl2br(htmlspecialchars($order['notes'])); ?></p>
        </div>
        <?php endif; ?>

        <!-- الباركود -->
        <div class="barcode-section">
            <div class="barcode"><?php echo $order['barcode']; ?></div>
            <div style="font-size: 10px; color: #666;">احتفظ بهذا الإيصال لاستلام جهازك</div>
        </div>

        <!-- تذييل الإيصال -->
        <div class="receipt-footer">
            <div class="thank-you">
                شكراً لثقتكم بنا - نتطلع لخدمتكم دائماً
            </div>
            <div style="font-size: 10px; color: #666;">
                هذا الإيصال صالح لمدة 30 يوماً من تاريخ الإصدار<br>
                في حالة فقدان الإيصال يرجى إحضار هوية شخصية
            </div>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.addEventListener('load', function() {
            // تأخير قصير للتأكد من تحميل كامل للصفحة
            setTimeout(function() {
                // window.print();
            }, 500);
        });
    </script>
</body>
</html>
