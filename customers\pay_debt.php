<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$customer_id = (int)($input['customer_id'] ?? 0);
$payment_type = sanitize($input['payment_type'] ?? '');
$payment_amount = (float)($input['payment_amount'] ?? 0);
$notes = sanitize($input['notes'] ?? '');

if (!$customer_id || !$payment_type) {
    echo json_encode(['success' => false, 'message' => 'البيانات المطلوبة غير مكتملة'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // تحديث ديون العميل أولاً
    updateCustomerDebt($customer_id);
    
    // الحصول على بيانات العميل
    $sql = "SELECT * FROM customers WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        echo json_encode(['success' => false, 'message' => 'العميل غير موجود'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    $total_debt = $customer['total_debt'];
    
    if ($total_debt <= 0) {
        echo json_encode(['success' => false, 'message' => 'لا يوجد ديون على هذا العميل'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    // تحديد مبلغ التسديد
    if ($payment_type === 'full') {
        $payment_amount = $total_debt;
    } elseif ($payment_type === 'partial') {
        if ($payment_amount <= 0 || $payment_amount > $total_debt) {
            echo json_encode(['success' => false, 'message' => 'مبلغ التسديد غير صحيح'], JSON_UNESCAPED_UNICODE);
            exit();
        }
    }
    
    // بدء المعاملة
    $conn->beginTransaction();
    
    // الحصول على رقم الوصل التالي
    $receipt_number = 'REC' . str_pad($database->getNextSequenceNumber('receipt'), 6, '0', STR_PAD_LEFT);
    
    // إدراج سجل الدفع
    $sql = "INSERT INTO payments (customer_id, payment_type, amount, payment_method, notes, receipt_number) 
            VALUES (?, 'debt_payment', ?, 'cash', ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id, $payment_amount, $notes, $receipt_number]);
    
    $payment_id = $conn->lastInsertId();
    
    // تحديث الطلبات المديونة
    $remaining_payment = $payment_amount;
    
    $sql = "SELECT id, remaining_amount FROM repair_orders 
            WHERE customer_id = ? AND remaining_amount > 0 
            ORDER BY created_at ASC";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $debt_orders = $stmt->fetchAll();
    
    foreach ($debt_orders as $order) {
        if ($remaining_payment <= 0) break;
        
        $order_payment = min($remaining_payment, $order['remaining_amount']);
        $new_remaining = $order['remaining_amount'] - $order_payment;
        $new_paid = $order_payment;
        
        // تحديث الطلب
        $sql = "UPDATE repair_orders 
                SET paid_amount = paid_amount + ?, remaining_amount = ? 
                WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$order_payment, $new_remaining, $order['id']]);
        
        $remaining_payment -= $order_payment;
    }
    
    // تحديث ديون العميل
    updateCustomerDebt($customer_id);
    
    // إنشاء مستند الوصل
    $document_data = [
        'receipt_number' => $receipt_number,
        'customer_name' => $customer['full_name'],
        'customer_phone' => $customer['phone'],
        'payment_amount' => $payment_amount,
        'payment_type' => $payment_type,
        'old_debt' => $total_debt,
        'new_debt' => $total_debt - $payment_amount,
        'notes' => $notes,
        'date' => date('Y-m-d H:i:s')
    ];
    
    $sql = "INSERT INTO documents (document_type, document_number, related_id, customer_id, document_data) 
            VALUES ('payment_receipt', ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$receipt_number, $payment_id, $customer_id, json_encode($document_data, JSON_UNESCAPED_UNICODE)]);
    
    $document_id = $conn->lastInsertId();
    
    // تأكيد المعاملة
    $conn->commit();
    
    // تسجيل العملية في السجل
    $database->logActivity('تسديد دين', 'payments', $payment_id, null, [
        'customer_id' => $customer_id,
        'payment_amount' => $payment_amount,
        'payment_type' => $payment_type,
        'receipt_number' => $receipt_number
    ], "تم تسديد دين للعميل: {$customer['full_name']} بمبلغ " . formatCurrency($payment_amount));
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تسديد الدين بنجاح',
        'receipt_id' => $document_id,
        'receipt_number' => $receipt_number,
        'payment_amount' => $payment_amount,
        'remaining_debt' => $total_debt - $payment_amount
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollBack();
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
