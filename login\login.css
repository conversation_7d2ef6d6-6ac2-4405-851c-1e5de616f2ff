* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.login-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    width: 450px;
    max-width: 90%;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 2;
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.logo i {
    font-size: 35px;
    color: white;
}

.login-header h1 {
    color: #333;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.login-header h2 {
    color: #667eea;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
}

.login-header p {
    color: #666;
    font-size: 14px;
}

.login-form {
    margin-bottom: 30px;
}

.error-message {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
}

.error-message i {
    margin-left: 8px;
}

.input-group {
    margin-bottom: 20px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border-radius: 12px;
    border: 2px solid #e1e5e9;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.input-wrapper:focus-within {
    border-color: #667eea;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
}

.input-wrapper i {
    padding: 0 15px;
    color: #667eea;
    font-size: 18px;
}

.input-wrapper input {
    flex: 1;
    padding: 15px 10px;
    border: none;
    outline: none;
    font-size: 16px;
    font-family: 'Cairo', sans-serif;
    background: transparent;
}

.input-wrapper input::placeholder {
    color: #999;
}

.toggle-password {
    background: none;
    border: none;
    padding: 0 15px;
    color: #667eea;
    cursor: pointer;
    font-size: 16px;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #764ba2;
}

.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 18px;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn i {
    margin-left: 8px;
}

.login-footer {
    text-align: center;
    color: #666;
    font-size: 14px;
}

.login-footer p {
    margin-bottom: 10px;
}

.login-footer i {
    margin-left: 5px;
    color: #667eea;
}

.contact-info {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e1e5e9;
}

.contact-info p {
    margin-bottom: 5px;
}

/* تأثيرات إضافية */
.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 20px 20px 0 0;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 480px) {
    .login-card {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .login-header h1 {
        font-size: 20px;
    }
    
    .login-header h2 {
        font-size: 16px;
    }
    
    .logo {
        width: 60px;
        height: 60px;
    }
    
    .logo i {
        font-size: 25px;
    }
}
