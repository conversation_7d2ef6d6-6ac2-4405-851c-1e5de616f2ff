<?php
// ملف إصلاح أخطاء النظام بعد تحديث قاعدة البيانات

// إعداد الترميز
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
mb_regex_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح أخطاء النظام</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; text-align: right; padding: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #e74c3c; background: #ffebee; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #3498db; background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #f39c12; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo "h1 { color: #2c3e50; text-align: center; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo ".btn { background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح أخطاء النظام</h1>";

try {
    require_once 'config/database.php';
    
    $database = new Database();
    $conn = $database->connect();
    
    if (!$conn) {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    
    echo "<div class='info'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // الخطوة 1: إصلاح جدول الإعدادات
    echo "<div class='step'>";
    echo "<h2>الخطوة 1: إصلاح جدول الإعدادات</h2>";
    
    // التحقق من هيكل جدول الإعدادات
    $check_settings = "SHOW COLUMNS FROM settings";
    $result = $conn->query($check_settings);
    $columns = $result->fetchAll();
    
    $has_setting_key = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'setting_key') {
            $has_setting_key = true;
            break;
        }
    }
    
    if (!$has_setting_key) {
        // إضافة العمود المفقود
        $conn->exec("ALTER TABLE settings ADD COLUMN setting_key VARCHAR(100) NOT NULL UNIQUE AFTER id");
        echo "<div class='success'>✅ تم إضافة عمود setting_key</div>";
        
        // نقل البيانات من setting_name إلى setting_key
        $conn->exec("UPDATE settings SET setting_key = setting_name WHERE setting_name IS NOT NULL");
        echo "<div class='success'>✅ تم نقل البيانات</div>";
        
        // حذف العمود القديم
        $conn->exec("ALTER TABLE settings DROP COLUMN setting_name");
        echo "<div class='success'>✅ تم حذف العمود القديم</div>";
    } else {
        echo "<div class='info'>✅ جدول الإعدادات صحيح</div>";
    }
    echo "</div>";
    
    // الخطوة 2: إصلاح جدول سجل العمليات
    echo "<div class='step'>";
    echo "<h2>الخطوة 2: إصلاح جدول سجل العمليات</h2>";
    
    // التحقق من وجود جدول activity_logs
    $check_logs = "SHOW TABLES LIKE 'activity_logs'";
    $result = $conn->query($check_logs);
    
    if ($result->rowCount() == 0) {
        // إنشاء جدول سجل العمليات
        $create_logs = "
        CREATE TABLE activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NULL,
            action_type VARCHAR(100) NOT NULL,
            table_name VARCHAR(50) NOT NULL,
            record_id INT NULL,
            old_data JSON NULL,
            new_data JSON NULL,
            description TEXT,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_action_type (action_type),
            INDEX idx_table_name (table_name),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($create_logs);
        echo "<div class='success'>✅ تم إنشاء جدول سجل العمليات</div>";
    } else {
        echo "<div class='info'>✅ جدول سجل العمليات موجود</div>";
    }
    echo "</div>";
    
    // الخطوة 3: إصلاح جدول التسلسل
    echo "<div class='step'>";
    echo "<h2>الخطوة 3: إصلاح جدول التسلسل</h2>";
    
    $check_sequences = "SHOW TABLES LIKE 'sequences'";
    $result = $conn->query($check_sequences);
    
    if ($result->rowCount() == 0) {
        $create_sequences = "
        CREATE TABLE sequences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            sequence_name VARCHAR(50) NOT NULL UNIQUE,
            current_value INT NOT NULL DEFAULT 0,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($create_sequences);
        echo "<div class='success'>✅ تم إنشاء جدول التسلسل</div>";
        
        // إدراج التسلسلات الافتراضية
        $sequences = [
            ['order', 0],
            ['receipt', 0], 
            ['barcode', 0]
        ];
        
        foreach ($sequences as $seq) {
            $sql = "INSERT INTO sequences (sequence_name, current_value) VALUES (?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute($seq);
        }
        echo "<div class='success'>✅ تم إدراج التسلسلات الافتراضية</div>";
    } else {
        echo "<div class='info'>✅ جدول التسلسل موجود</div>";
    }
    echo "</div>";
    
    // الخطوة 4: إصلاح دالة getNextSequenceNumber
    echo "<div class='step'>";
    echo "<h2>الخطوة 4: اختبار دوال قاعدة البيانات</h2>";
    
    try {
        // اختبار دالة getNextSequenceNumber
        $next_number = $database->getNextSequenceNumber('order');
        echo "<div class='success'>✅ دالة getNextSequenceNumber تعمل بشكل صحيح - الرقم التالي: $next_number</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في دالة getNextSequenceNumber: " . $e->getMessage() . "</div>";
    }
    
    try {
        // اختبار دالة logActivity
        $database->logActivity('اختبار النظام', 'test', 1, null, ['test' => 'data'], 'اختبار تسجيل العمليات');
        echo "<div class='success'>✅ دالة logActivity تعمل بشكل صحيح</div>";
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في دالة logActivity: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // الخطوة 5: اختبار إضافة العملاء
    echo "<div class='step'>";
    echo "<h2>الخطوة 5: اختبار إضافة العملاء</h2>";
    
    try {
        $test_customer = "عميل اختبار النظام";
        $test_phone = "07700000000";
        $test_address = "عنوان اختبار النظام";
        
        $sql = "INSERT INTO customers (full_name, phone, address) VALUES (?, ?, ?) 
                ON DUPLICATE KEY UPDATE full_name = VALUES(full_name)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$test_customer, $test_phone, $test_address]);
        
        echo "<div class='success'>✅ إضافة العملاء تعمل بشكل صحيح</div>";
        
        // حذف العميل التجريبي
        $sql = "DELETE FROM customers WHERE phone = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$test_phone]);
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في إضافة العملاء: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // الخطوة 6: اختبار إضافة قطع الغيار
    echo "<div class='step'>";
    echo "<h2>الخطوة 6: اختبار إضافة قطع الغيار</h2>";
    
    try {
        $test_part = "قطعة غيار اختبار";
        $sql = "INSERT INTO spare_parts (part_name, purchase_price, selling_price, quantity) VALUES (?, 100, 150, 5)
                ON DUPLICATE KEY UPDATE part_name = VALUES(part_name)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$test_part]);
        
        echo "<div class='success'>✅ إضافة قطع الغيار تعمل بشكل صحيح</div>";
        
        // حذف القطعة التجريبية
        $sql = "DELETE FROM spare_parts WHERE part_name = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$test_part]);
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ خطأ في إضافة قطع الغيار: " . $e->getMessage() . "</div>";
    }
    echo "</div>";
    
    // الخطوة 7: إصلاح ملفات CSS
    echo "<div class='step'>";
    echo "<h2>الخطوة 7: التحقق من ملفات CSS</h2>";
    
    $css_files = [
        'dashboard/dashboard.css',
        'repair/repair.css',
        'customers/customers.css',
        'parts/parts.css'
    ];
    
    foreach ($css_files as $css_file) {
        if (file_exists($css_file)) {
            echo "<div class='success'>✅ ملف $css_file موجود</div>";
        } else {
            echo "<div class='warning'>⚠️ ملف $css_file مفقود</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🎉 تم إصلاح جميع أخطاء النظام!</h2>";
    echo "<div class='success'>";
    echo "<h3>الإصلاحات المُنجزة:</h3>";
    echo "<ul>";
    echo "<li>✅ إصلاح جدول الإعدادات</li>";
    echo "<li>✅ إصلاح جدول سجل العمليات</li>";
    echo "<li>✅ إصلاح جدول التسلسل</li>";
    echo "<li>✅ اختبار دوال قاعدة البيانات</li>";
    echo "<li>✅ اختبار إضافة العملاء</li>";
    echo "<li>✅ اختبار إضافة قطع الغيار</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>الآن يمكنك:</h3>";
    echo "<ul>";
    echo "<li>تسجيل الدخول للنظام</li>";
    echo "<li>إضافة عملاء جدد</li>";
    echo "<li>إضافة قطع غيار</li>";
    echo "<li>إضافة طلبات صيانة</li>";
    echo "<li>استخدام جميع ميزات النظام</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ حدث خطأ عام: " . $e->getMessage() . "</div>";
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='index.php' class='btn'>العودة للنظام</a>";
echo "<a href='dashboard/dashboard.php' class='btn'>لوحة التحكم</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
