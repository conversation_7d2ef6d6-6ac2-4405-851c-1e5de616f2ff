<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$part_id = (int)($input['id'] ?? 0);
$part_name = sanitize($input['part_name'] ?? '');
$purchase_price = (float)($input['purchase_price'] ?? 0);
$selling_price = (float)($input['selling_price'] ?? 0);
$quantity = (int)($input['quantity'] ?? 0);
$barcode = sanitize($input['barcode'] ?? '');
$notes = sanitize($input['notes'] ?? '');

// التحقق من البيانات المطلوبة
if (!$part_id || empty($part_name) || $purchase_price < 0 || $selling_price < 0 || $quantity < 0) {
    echo json_encode(['success' => false, 'message' => 'جميع البيانات المطلوبة يجب أن تكون صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

// التحقق من عدم تكرار الباركود (باستثناء القطعة الحالية)
if (!empty($barcode)) {
    $sql = "SELECT id FROM spare_parts WHERE barcode = ? AND id != ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$barcode, $part_id]);
    if ($stmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'الباركود موجود مسبقاً لقطعة أخرى'], JSON_UNESCAPED_UNICODE);
        exit();
    }
}

try {
    // الحصول على البيانات القديمة للسجل
    $sql = "SELECT * FROM spare_parts WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$part_id]);
    $old_data = $stmt->fetch();
    
    if (!$old_data) {
        echo json_encode(['success' => false, 'message' => 'القطعة غير موجودة'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    // تحديث بيانات القطعة
    $sql = "UPDATE spare_parts SET part_name = ?, purchase_price = ?, selling_price = ?, 
            quantity = ?, barcode = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$part_name, $purchase_price, $selling_price, $quantity, $barcode, $notes, $part_id]);
    
    // تسجيل العملية في السجل
    $new_data = [
        'part_name' => $part_name,
        'purchase_price' => $purchase_price,
        'selling_price' => $selling_price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'notes' => $notes
    ];
    
    $database->logActivity('تحديث قطعة غيار', 'spare_parts', $part_id, $old_data, $new_data, "تم تحديث قطعة الغيار: $part_name");
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديث القطعة بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
