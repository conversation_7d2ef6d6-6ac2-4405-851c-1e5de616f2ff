<?php
require_once '../config/init.php';
checkLogin();

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$document_type = isset($_GET['type']) ? sanitize($_GET['type']) : '';
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(d.document_number LIKE ? OR c.full_name LIKE ? OR c.phone LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if (!empty($document_type)) {
    $where_conditions[] = "d.document_type = ?";
    $params[] = $document_type;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(d.created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(d.created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد المستندات
$count_sql = "SELECT COUNT(*) as total FROM documents d 
               JOIN customers c ON d.customer_id = c.id 
               $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_documents = $count_stmt->fetch()['total'];
$total_pages = ceil($total_documents / $per_page);

// الحصول على المستندات
$sql = "SELECT d.*, c.full_name, c.phone 
        FROM documents d 
        JOIN customers c ON d.customer_id = c.id 
        $where_clause 
        ORDER BY d.created_at DESC 
        LIMIT $per_page OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$documents = $stmt->fetchAll();

// حساب إحصائيات سريعة
$stats_sql = "SELECT 
    COUNT(*) as total_documents,
    COUNT(CASE WHEN document_type = 'repair_receipt' THEN 1 END) as repair_receipts,
    COUNT(CASE WHEN document_type = 'payment_receipt' THEN 1 END) as payment_receipts,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_documents
    FROM documents";
$stats_stmt = $conn->prepare($stats_sql);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

// دالة لترجمة نوع المستند
function translateDocumentType($type) {
    switch($type) {
        case 'repair_receipt': return 'إيصال صيانة';
        case 'payment_receipt': return 'وصل تسديد';
        default: return 'غير محدد';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - إدارة المستندات</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="../repair/repair.css">
    <link rel="stylesheet" href="documents.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="../repair/add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="../repair/view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="documents.php" class="nav-item active">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-file-alt"></i> إدارة المستندات</h1>
                <p>عرض وإدارة جميع المستندات والإيصالات</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-success" onclick="exportDocuments()">
                    <i class="fas fa-download"></i> تصدير المستندات
                </button>
                <button class="btn btn-primary" onclick="printAllDocuments()">
                    <i class="fas fa-print"></i> طباعة مجمعة
                </button>
            </div>
        </header>

        <!-- أدوات البحث والفلترة -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <label>البحث</label>
                    <input type="text" name="search" placeholder="البحث في المستندات..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="filter-group">
                    <label>نوع المستند</label>
                    <select name="type">
                        <option value="">جميع الأنواع</option>
                        <option value="repair_receipt" <?php echo $document_type === 'repair_receipt' ? 'selected' : ''; ?>>إيصالات الصيانة</option>
                        <option value="payment_receipt" <?php echo $document_type === 'payment_receipt' ? 'selected' : ''; ?>>وصولات التسديد</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>من تاريخ</label>
                    <input type="date" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                
                <div class="filter-group">
                    <label>إلى تاريخ</label>
                    <input type="date" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="documents.php" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card documents">
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['total_documents']); ?></h3>
                    <p>إجمالي المستندات</p>
                </div>
            </div>
            
            <div class="stat-card repair-receipts">
                <div class="stat-icon">
                    <i class="fas fa-receipt"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['repair_receipts']); ?></h3>
                    <p>إيصالات الصيانة</p>
                </div>
            </div>
            
            <div class="stat-card payment-receipts">
                <div class="stat-icon">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['payment_receipts']); ?></h3>
                    <p>وصولات التسديد</p>
                </div>
            </div>
            
            <div class="stat-card today">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['today_documents']); ?></h3>
                    <p>مستندات اليوم</p>
                </div>
            </div>
        </div>

        <!-- جدول المستندات -->
        <div class="table-container">
            <?php if (empty($documents)): ?>
            <div class="no-data">
                <i class="fas fa-file-alt"></i>
                <h3>لا توجد مستندات</h3>
                <p>لم يتم العثور على أي مستندات تطابق معايير البحث</p>
            </div>
            <?php else: ?>
            <table class="documents-table">
                <thead>
                    <tr>
                        <th>رقم المستند</th>
                        <th>نوع المستند</th>
                        <th>العميل</th>
                        <th>التاريخ</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($documents as $document): ?>
                    <?php 
                    $document_data = json_decode($document['document_data'], true);
                    $icon_class = $document['document_type'] === 'repair_receipt' ? 'fas fa-wrench' : 'fas fa-money-bill-wave';
                    $type_class = $document['document_type'] === 'repair_receipt' ? 'repair' : 'payment';
                    ?>
                    <tr>
                        <td>
                            <div class="document-number">
                                <i class="<?php echo $icon_class; ?>"></i>
                                <strong><?php echo htmlspecialchars($document['document_number']); ?></strong>
                            </div>
                        </td>
                        <td>
                            <span class="document-type type-<?php echo $type_class; ?>">
                                <?php echo translateDocumentType($document['document_type']); ?>
                            </span>
                        </td>
                        <td>
                            <div class="customer-info">
                                <strong><?php echo htmlspecialchars($document['full_name']); ?></strong>
                                <br>
                                <small><?php echo htmlspecialchars($document['phone']); ?></small>
                            </div>
                        </td>
                        <td>
                            <div class="document-date">
                                <?php echo date('Y-m-d', strtotime($document['created_at'])); ?>
                                <br>
                                <small><?php echo date('H:i', strtotime($document['created_at'])); ?></small>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-active">
                                <i class="fas fa-check-circle"></i>
                                نشط
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view" onclick="viewDocument(<?php echo $document['id']; ?>)" title="عرض المستند">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-print" onclick="printDocument(<?php echo $document['id']; ?>, '<?php echo $document['document_type']; ?>')" title="طباعة">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn-action btn-download" onclick="downloadDocument(<?php echo $document['id']; ?>)" title="تحميل PDF">
                                    <i class="fas fa-download"></i>
                                </button>
                                <button class="btn-action btn-email" onclick="emailDocument(<?php echo $document['id']; ?>)" title="إرسال بالإيميل">
                                    <i class="fas fa-envelope"></i>
                                </button>
                                <button class="btn-action btn-delete" onclick="deleteDocument(<?php echo $document['id']; ?>)" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- ترقيم الصفحات -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($document_type); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" class="page-btn">
                    <i class="fas fa-chevron-right"></i> السابق
                </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($document_type); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" 
                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                    <?php echo $i; ?>
                </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&type=<?php echo urlencode($document_type); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" class="page-btn">
                    التالي <i class="fas fa-chevron-left"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- نافذة عرض المستند -->
    <div id="documentModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3 id="documentModalTitle"><i class="fas fa-file-alt"></i> عرض المستند</h3>
                <button class="modal-close" onclick="closeDocumentModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="documentContent">
                    <!-- سيتم تحميل محتوى المستند هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="printCurrentDocument()">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-secondary" onclick="closeDocumentModal()">
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- نافذة إرسال بالإيميل -->
    <div id="emailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-envelope"></i> إرسال المستند بالإيميل</h3>
                <button class="modal-close" onclick="closeEmailModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="emailForm">
                <div class="modal-body">
                    <input type="hidden" id="email_document_id">
                    
                    <div class="form-group">
                        <label for="recipient_email">البريد الإلكتروني للمستلم *</label>
                        <input type="email" id="recipient_email" required placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="email_subject">موضوع الرسالة</label>
                        <input type="text" id="email_subject" placeholder="سيتم إنشاؤه تلقائياً">
                    </div>
                    
                    <div class="form-group">
                        <label for="email_message">نص الرسالة</label>
                        <textarea id="email_message" placeholder="رسالة إضافية (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i> إرسال
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeEmailModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="documents.js"></script>
</body>
</html>
