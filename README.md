# نظام إدارة صيانة الموبايلات

نظام شامل لإدارة مركز صيانة الموبايلات مطور بـ PHP, HTML, CSS, JavaScript

## المميزات الرئيسية

### 🔐 نظام تسجيل الدخول الآمن
- واجهة تسجيل دخول جميلة وآمنة
- اسم المستخدم: `abd`
- كلمة المرور: `ZAin1998`

### 📊 لوحة التحكم الرئيسية
- إحصائيات شاملة ومباشرة
- بحث شامل في جميع أجزاء النظام
- إجراءات سريعة للمهام الأساسية

### 🔧 إدارة طلبات الصيانة
- إضافة طلبات صيانة جديدة
- إنشاء إيصالات تلقائية (A5, A4, طابعة حرارية)
- تتبع حالة الطلبات
- تحديث حالات الطلبات (قيد الصيانة، مكتمل، فشل، إلخ)
- طباعة الإيصالات

### 👥 إدارة العملاء
- إضافة وتعديل بيانات العملاء
- تتبع ديون العملاء
- تسديد الديون (كامل أو جزئي)
- إنشاء وصولات تسديد

### ⚙️ إدارة قطع الغيار
- إضافة وإدارة قطع الغيار
- استيراد من ملفات Excel
- تصدير إلى Excel و PDF
- تتبع المخزون

### 📄 إدارة المستندات
- عرض جميع المستندات
- طباعة الإيصالات والوصولات
- أرشفة تلقائية

### 📈 التقارير والإحصائيات
- تقارير شاملة لجميع العمليات
- تقارير حسب الفترات الزمنية
- إحصائيات المبيعات والديون

### 📝 سجل العمليات
- تتبع جميع التغييرات والعمليات
- سجل مفصل لكل إجراء
- بحث في السجل

### ⚙️ الإعدادات
- تخصيص معلومات المركز
- إعدادات الطباعة
- إدارة المستخدمين
- النسخ الاحتياطي

## متطلبات التشغيل

### الخادم
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache أو Nginx

### قاعدة البيانات
- اسم قاعدة البيانات: `irjnpfzw_seana`
- اسم المستخدم: `irjnpfzw_seana`
- كلمة المرور: `irjnpfzw_seana`

## التثبيت

### 1. رفع الملفات
```bash
# رفع جميع ملفات النظام إلى مجلد الخادم
```

### 2. إعداد قاعدة البيانات
```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE irjnpfzw_seana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء المستخدم
CREATE USER 'irjnpfzw_seana'@'localhost' IDENTIFIED BY 'irjnpfzw_seana';
GRANT ALL PRIVILEGES ON irjnpfzw_seana.* TO 'irjnpfzw_seana'@'localhost';
FLUSH PRIVILEGES;
```

### 3. تعديل إعدادات قاعدة البيانات (اختياري)
```php
// في ملف config/database.php
private $host = 'localhost';
private $db_name = 'اسم_قاعدة_البيانات_الجديد';
private $username = 'اسم_المستخدم_الجديد';
private $password = 'كلمة_المرور_الجديدة';
```

### 4. الوصول للنظام
- افتح المتصفح وانتقل إلى: `http://your-domain.com/`
- سيتم توجيهك تلقائياً إلى صفحة تسجيل الدخول

## هيكل المشروع

```
├── config/                 # ملفات الإعدادات
│   ├── database.php        # إعدادات قاعدة البيانات
│   └── init.php           # ملف التهيئة الأساسي
├── login/                  # نظام تسجيل الدخول
│   ├── login.php          # صفحة تسجيل الدخول
│   ├── login.css          # تنسيقات تسجيل الدخول
│   ├── login.js           # سكريبت تسجيل الدخول
│   └── logout.php         # تسجيل الخروج
├── dashboard/              # لوحة التحكم
│   ├── dashboard.php      # الصفحة الرئيسية
│   ├── dashboard.css      # تنسيقات لوحة التحكم
│   └── dashboard.js       # سكريبت لوحة التحكم
├── repair/                 # نظام الصيانة
│   ├── add_repair.php     # إضافة طلب صيانة
│   ├── view_repairs.php   # عرض الطلبات
│   ├── print_receipt.php  # طباعة الإيصال
│   ├── repair.css         # تنسيقات الصيانة
│   ├── repair.js          # سكريبت الصيانة
│   └── view_repairs.js    # سكريبت عرض الطلبات
├── customers/              # إدارة العملاء
│   └── add_customer_ajax.php # إضافة عميل عبر AJAX
└── index.php              # الصفحة الرئيسية
```

## الاستخدام

### تسجيل الدخول
1. انتقل إلى الصفحة الرئيسية
2. أدخل اسم المستخدم: `abd`
3. أدخل كلمة المرور: `ZAin1998`
4. اضغط "تسجيل الدخول"

### إضافة طلب صيانة جديد
1. من لوحة التحكم، اضغط "إضافة صيانة"
2. اختر العميل أو أضف عميل جديد
3. أدخل تفاصيل الجهاز والمشكلة
4. حدد التكلفة وطريقة الدفع
5. اضغط "إضافة طلب الصيانة"
6. سيتم إنشاء الإيصال تلقائياً

### البحث الشامل
- استخدم خانة البحث في أعلى لوحة التحكم
- يمكن البحث بـ: اسم العميل، رقم الهاتف، رقم الطلب، الباركود
- النتائج تظهر فوراً مع إمكانية النقر للانتقال

## العملة
النظام يعمل بالدينار العراقي كعملة افتراضية

## الدعم الفني
- النظام مطور بتقنيات حديثة وآمنة
- واجهات سهلة الاستخدام ومتجاوبة
- دعم الطباعة بأحجام مختلفة
- نظام أرشفة تلقائي

## الأمان
- حماية من SQL Injection
- تشفير كلمات المرور
- تسجيل جميع العمليات
- جلسات آمنة

## التطوير المستقبلي
- دعم عدة مستخدمين
- تطبيق موبايل
- إشعارات تلقائية
- تكامل مع أنظمة الدفع

---

**تم تطوير النظام بعناية فائقة لضمان سهولة الاستخدام والأمان والكفاءة**
