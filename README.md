# 🔧 نظام إدارة صيانة الموبايلات الشامل

نظام متكامل وشامل لإدارة مراكز صيانة الموبايلات مطور بـ **PHP, HTML, CSS, JavaScript** مع دعم كامل للغة العربية وتصميم عصري متجاوب.

## ✨ المميزات الرئيسية

### 🔐 نظام تسجيل الدخول الآمن
- واجهة تسجيل دخول جميلة وآمنة مع تأثيرات بصرية
- حماية من هجمات القوة الغاشمة
- تشفير كلمات المرور بـ bcrypt
- **بيانات الدخول الافتراضية:**
  - اسم المستخدم: `abd`
  - كلمة المرور: `ZAin1998`

### 📊 لوحة التحكم الرئيسية المتطورة
- إحصائيات شاملة ومباشرة مع رسوم بيانية
- بحث شامل ذكي في جميع أجزاء النظام
- إجراءات سريعة للمهام الأساسية
- تحديث البيانات في الوقت الفعلي
- واجهة متجاوبة تعمل على جميع الأجهزة

### 🔧 إدارة طلبات الصيانة المتقدمة
- إضافة طلبات صيانة جديدة مع واجهة سهلة
- إنشاء إيصالات تلقائية بتصاميم احترافية (A5, A4, طابعة حرارية)
- تتبع حالة الطلبات بألوان مميزة
- تحديث حالات الطلبات (قيد الصيانة، مكتمل، فشل، انتظار قطع، مُسلم)
- طباعة الإيصالات بجودة عالية
- نظام باركود فريد لكل طلب
- إشعارات تلقائية للعملاء

### 👥 إدارة العملاء الشاملة
- إضافة وتعديل بيانات العملاء مع واجهة بديهية
- تتبع ديون العملاء تلقائياً
- تسديد الديون (كامل أو جزئي) مع إنشاء وصولات
- عرض تاريخ العميل الكامل
- بحث متقدم في العملاء
- إحصائيات مفصلة لكل عميل

### ⚙️ إدارة قطع الغيار المتطورة
- إضافة وإدارة قطع الغيار مع تتبع المخزون
- استيراد من ملفات Excel بسهولة
- تصدير إلى Excel و PDF
- تتبع المخزون مع تنبيهات النفاد
- حساب الأرباح تلقائياً
- نظام باركود لقطع الغيار
- تقارير مخزون مفصلة

### 📄 إدارة المستندات والأرشفة
- عرض جميع المستندات في مكان واحد
- طباعة الإيصالات والوصولات
- أرشفة تلقائية منظمة
- بحث في المستندات
- تصدير المستندات
- إرسال المستندات بالإيميل

### 📈 التقارير والإحصائيات المتقدمة
- تقارير شاملة لجميع العمليات
- تقارير حسب الفترات الزمنية
- إحصائيات المبيعات والديون
- رسوم بيانية تفاعلية
- تقارير العملاء الأفضل
- تحليل الأداء اليومي والشهري

### 📝 سجل العمليات المفصل
- تتبع جميع التغييرات والعمليات
- سجل مفصل لكل إجراء مع التوقيت
- بحث متقدم في السجل
- تصفية حسب نوع العملية
- حفظ تاريخ كامل للنظام

### ⚙️ الإعدادات الشاملة
- تخصيص معلومات المركز
- إعدادات الطباعة المتقدمة
- إدارة المستخدمين والصلاحيات
- النسخ الاحتياطي التلقائي
- إعدادات الإشعارات
- تحسين قاعدة البيانات

## 🛠️ متطلبات التشغيل

### متطلبات الخادم
- **PHP:** 7.4 أو أحدث (يُفضل 8.0+)
- **MySQL:** 5.7 أو أحدث (يُفضل 8.0+)
- **خادم الويب:** Apache أو Nginx
- **الذاكرة:** 512 MB RAM كحد أدنى
- **المساحة:** 100 MB مساحة تخزين

### إعدادات قاعدة البيانات الافتراضية
- **اسم قاعدة البيانات:** `irjnpfzw_seana`
- **اسم المستخدم:** `irjnpfzw_seana`
- **كلمة المرور:** `irjnpfzw_seana`
- **ترميز الأحرف:** `utf8mb4_unicode_ci` (دعم كامل للعربية)

## 🚀 دليل التثبيت السريع

### الخطوة 1: رفع الملفات
```bash
# رفع جميع ملفات النظام إلى مجلد الخادم
# تأكد من رفع جميع المجلدات والملفات
```

### الخطوة 2: إعداد قاعدة البيانات
```sql
-- تشغيل ملف database_setup.sql
-- أو تنفيذ الأوامر التالية:

CREATE DATABASE irjnpfzw_seana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'irjnpfzw_seana'@'localhost' IDENTIFIED BY 'irjnpfzw_seana';
GRANT ALL PRIVILEGES ON irjnpfzw_seana.* TO 'irjnpfzw_seana'@'localhost';
FLUSH PRIVILEGES;

-- ثم استيراد ملف database_setup.sql
```

### الخطوة 3: تخصيص الإعدادات (اختياري)
```php
// تعديل ملف config/database.php حسب إعدادات الخادم
private $host = 'localhost';           // عنوان الخادم
private $db_name = 'irjnpfzw_seana';   // اسم قاعدة البيانات
private $username = 'irjnpfzw_seana';  // اسم المستخدم
private $password = 'irjnpfzw_seana';  // كلمة المرور
```

### الخطوة 4: الوصول للنظام
1. افتح المتصفح وانتقل إلى: `http://your-domain.com/`
2. سيتم توجيهك تلقائياً إلى صفحة تسجيل الدخول
3. استخدم بيانات الدخول الافتراضية

## 📁 هيكل المشروع المنظم

```
نظام-إدارة-صيانة-الموبايلات/
├── 📁 config/                    # ملفات الإعدادات الأساسية
│   ├── 📄 database.php          # إعدادات قاعدة البيانات
│   └── 📄 init.php             # ملف التهيئة الأساسي
├── 📁 login/                     # نظام تسجيل الدخول
│   ├── 📄 login.php            # صفحة تسجيل الدخول
│   ├── 📄 login.css            # تنسيقات تسجيل الدخول
│   ├── 📄 login.js             # سكريبت تسجيل الدخول
│   └── 📄 logout.php           # تسجيل الخروج
├── 📁 dashboard/                 # لوحة التحكم الرئيسية
│   ├── 📄 dashboard.php        # الصفحة الرئيسية
│   ├── 📄 dashboard.css        # تنسيقات لوحة التحكم
│   └── 📄 dashboard.js         # سكريبت لوحة التحكم
├── 📁 repair/                    # نظام إدارة الصيانة
│   ├── 📄 add_repair.php       # إضافة طلب صيانة
│   ├── 📄 view_repairs.php     # عرض الطلبات
│   ├── 📄 print_receipt.php    # طباعة الإيصال
│   ├── 📄 repair.css           # تنسيقات الصيانة
│   ├── 📄 repair.js            # سكريبت الصيانة
│   └── 📄 view_repairs.js      # سكريبت عرض الطلبات
├── 📁 customers/                 # إدارة العملاء
│   ├── 📄 customers.php        # صفحة العملاء الرئيسية
│   ├── 📄 customers.css        # تنسيقات العملاء
│   ├── 📄 customers.js         # سكريبت العملاء
│   ├── 📄 add_customer.php     # إضافة عميل
│   ├── 📄 pay_debt.php         # تسديد الديون
│   └── 📄 print_payment_receipt.php # طباعة وصل التسديد
├── 📁 parts/                     # إدارة قطع الغيار
│   ├── 📄 parts.php            # صفحة قطع الغيار
│   ├── 📄 parts.css            # تنسيقات قطع الغيار
│   ├── 📄 parts.js             # سكريبت قطع الغيار
│   └── 📄 add_part.php         # إضافة قطعة غيار
├── 📁 documents/                 # إدارة المستندات
│   ├── 📄 documents.php        # صفحة المستندات
│   ├── 📄 documents.css        # تنسيقات المستندات
│   └── 📄 documents.js         # سكريبت المستندات
├── 📁 reports/                   # التقارير والإحصائيات
│   ├── 📄 reports.php          # صفحة التقارير
│   ├── 📄 reports.css          # تنسيقات التقارير
│   └── 📄 reports.js           # سكريبت التقارير
├── 📁 logs/                      # سجل العمليات
│   ├── 📄 logs.php             # صفحة السجل
│   ├── 📄 logs.css             # تنسيقات السجل
│   └── 📄 logs.js              # سكريبت السجل
├── 📁 settings/                  # إعدادات النظام
│   ├── 📄 settings.php         # صفحة الإعدادات
│   ├── 📄 settings.css         # تنسيقات الإعدادات
│   └── 📄 settings.js          # سكريبت الإعدادات
├── 📄 index.php                 # الصفحة الرئيسية
├── 📄 database_setup.sql        # ملف إعداد قاعدة البيانات
└── 📄 README.md                 # دليل المستخدم
```
