/* ملف CSS لإدارة العملاء */

.customers-container {
    margin-top: 20px;
}

.customers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.customer-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.customer-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
}

.customer-header {
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    display: flex;
    align-items: flex-start;
    gap: 15px;
    position: relative;
}

.customer-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
    flex-shrink: 0;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.customer-info {
    flex: 1;
}

.customer-info h3 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
}

.customer-info p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customer-info p i {
    color: #3498db;
    width: 16px;
}

.customer-actions {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.customer-stats {
    padding: 15px 20px;
    background: white;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    border-top: 1px solid #e9ecef;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 3px;
}

.stat-label {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
}

.debt-stat .stat-number {
    color: #e74c3c;
}

.debt-actions {
    padding: 15px 20px;
    background: #fff3cd;
    border-top: 1px solid #ffeaa7;
    text-align: center;
}

.customer-footer {
    padding: 10px 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    text-align: center;
}

.customer-footer small {
    color: #6c757d;
    font-size: 12px;
}

/* نافذة تسديد الدين */
.debt-info {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    border: 2px solid #f39c12;
}

.debt-info h4 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
}

.debt-amount {
    font-size: 20px;
    font-weight: 700;
    color: #e74c3c;
}

/* تحسينات للأزرار */
.btn-sm {
    padding: 8px 15px;
    font-size: 12px;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 3px 10px rgba(243, 156, 18, 0.3);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

/* تأثيرات إضافية */
.customer-card {
    position: relative;
    overflow: hidden;
}

.customer-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #3498db, #2980b9);
    transition: all 0.3s ease;
}

.customer-card:hover::before {
    width: 8px;
}

/* حالة العميل المديون */
.customer-card[data-has-debt="true"] {
    border-color: #f39c12;
}

.customer-card[data-has-debt="true"]::before {
    background: linear-gradient(180deg, #f39c12, #e67e22);
}

/* تأثيرات الحركة */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.customer-card {
    animation: slideInUp 0.6s ease-out;
}

/* تحسينات للنوافذ المنبثقة */
.modal-content {
    max-width: 600px;
}

.form-group textarea {
    min-height: 80px;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .customers-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .customer-header {
        flex-direction: column;
        text-align: center;
    }
    
    .customer-actions {
        flex-direction: row;
        justify-content: center;
        margin-top: 15px;
    }
    
    .customer-stats {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .stat-item {
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }
}

/* تحسينات إضافية للتصميم */
.customer-card .btn-action {
    width: 30px;
    height: 30px;
    font-size: 12px;
}

.customer-info p {
    word-break: break-word;
}

/* تأثير التحميل */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* تحسينات للطباعة */
@media print {
    .customer-actions,
    .debt-actions,
    .pagination {
        display: none !important;
    }
    
    .customer-card {
        break-inside: avoid;
        margin-bottom: 20px;
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
    
    .customers-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
}

/* تأثيرات خاصة للعملاء المميزين */
.customer-card.vip {
    border-color: #f1c40f;
    background: linear-gradient(135deg, #fff, #fffbf0);
}

.customer-card.vip::before {
    background: linear-gradient(180deg, #f1c40f, #f39c12);
}

.customer-card.vip .customer-avatar {
    background: linear-gradient(135deg, #f1c40f, #f39c12);
}

/* تحسينات للنصوص العربية */
.customer-info h3,
.customer-info p,
.stat-label {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
}

/* ضمان عرض النصوص العربية بشكل صحيح */
* {
    font-family: 'Cairo', sans-serif !important;
}

input, textarea, select {
    direction: rtl;
    text-align: right;
}

input::placeholder,
textarea::placeholder {
    direction: rtl;
    text-align: right;
    opacity: 0.7;
}
