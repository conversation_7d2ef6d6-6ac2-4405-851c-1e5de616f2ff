<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة']);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة']);
    exit();
}

$full_name = sanitize($input['full_name'] ?? '');
$phone = sanitize($input['phone'] ?? '');
$address = sanitize($input['address'] ?? '');

// التحقق من البيانات المطلوبة
if (empty($full_name) || empty($phone)) {
    echo json_encode(['success' => false, 'message' => 'الاسم ورقم الهاتف مطلوبان']);
    exit();
}

// التحقق من عدم تكرار رقم الهاتف
$sql = "SELECT id FROM customers WHERE phone = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$phone]);
if ($stmt->fetch()) {
    echo json_encode(['success' => false, 'message' => 'رقم الهاتف موجود مسبقاً']);
    exit();
}

try {
    // إدراج العميل الجديد
    $sql = "INSERT INTO customers (full_name, phone, address) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$full_name, $phone, $address]);
    
    $customer_id = $conn->lastInsertId();
    
    // تسجيل العملية في السجل
    $database->logActivity('إضافة عميل', 'customers', $customer_id, null, [
        'full_name' => $full_name,
        'phone' => $phone,
        'address' => $address
    ], "تم إضافة عميل جديد: $full_name");
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم إضافة العميل بنجاح',
        'customer_id' => $customer_id
    ]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()]);
}
?>
