<?php
// ملف التهيئة الأساسي للنظام

// إعداد الترميز للغة العربية في بداية كل شيء
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
mb_regex_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

session_start();

// تضمين ملف قاعدة البيانات
require_once 'database.php';

// إنشاء اتصال قاعدة البيانات
$database = new Database();
$conn = $database->connect();

// إنشاء الجداول إذا لم تكن موجودة
$database->createTables();

// دالة للتحقق من تسجيل الدخول
function checkLogin() {
    if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        header('Location: ../login/login.php');
        exit();
    }
}

// دالة لتنظيف البيانات
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

// دالة لتنسيق الأرقام بالدينار العراقي
function formatCurrency($amount) {
    return number_format($amount, 0, '.', ',') . ' دينار عراقي';
}

// دالة لتوليد باركود
function generateBarcode($text) {
    return 'BC' . str_pad($text, 8, '0', STR_PAD_LEFT);
}

// دالة للحصول على إعداد من قاعدة البيانات
function getSetting($setting_name, $default = '') {
    global $conn;
    $sql = "SELECT setting_value FROM settings WHERE setting_name = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$setting_name]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

// دالة لتحديث إعداد في قاعدة البيانات
function updateSetting($setting_name, $setting_value) {
    global $conn;
    $sql = "UPDATE settings SET setting_value = ? WHERE setting_name = ?";
    $stmt = $conn->prepare($sql);
    return $stmt->execute([$setting_value, $setting_name]);
}

// دالة للبحث الشامل
function globalSearch($query) {
    global $conn;
    $results = [];
    
    // البحث في العملاء
    $sql = "SELECT 'customer' as type, id, full_name as title, phone as subtitle, 'customers' as table_name 
            FROM customers WHERE full_name LIKE ? OR phone LIKE ?";
    $stmt = $conn->prepare($sql);
    $search_term = "%$query%";
    $stmt->execute([$search_term, $search_term]);
    $results = array_merge($results, $stmt->fetchAll());
    
    // البحث في طلبات الصيانة
    $sql = "SELECT 'order' as type, r.id, CONCAT('طلب رقم ', r.order_number) as title, 
            CONCAT(c.full_name, ' - ', r.problem_description) as subtitle, 'repair_orders' as table_name
            FROM repair_orders r 
            JOIN customers c ON r.customer_id = c.id 
            WHERE r.order_number LIKE ? OR r.problem_description LIKE ? OR r.barcode LIKE ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$search_term, $search_term, $search_term]);
    $results = array_merge($results, $stmt->fetchAll());
    
    // البحث في قطع الغيار
    $sql = "SELECT 'part' as type, id, part_name as title, 
            CONCAT('الكمية: ', quantity, ' - السعر: ', selling_price) as subtitle, 'spare_parts' as table_name
            FROM spare_parts WHERE part_name LIKE ? OR barcode LIKE ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$search_term, $search_term]);
    $results = array_merge($results, $stmt->fetchAll());
    
    return $results;
}

// دالة لحساب الإحصائيات
function getStatistics() {
    global $conn;
    
    $stats = [];
    
    // عدد العملاء
    $sql = "SELECT COUNT(*) as count FROM customers";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['customers'] = $stmt->fetch()['count'];
    
    // عدد الطلبات
    $sql = "SELECT COUNT(*) as count FROM repair_orders";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['orders'] = $stmt->fetch()['count'];
    
    // عدد الطلبات المكتملة
    $sql = "SELECT COUNT(*) as count FROM repair_orders WHERE status = 'completed'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['completed_orders'] = $stmt->fetch()['count'];
    
    // عدد قطع الغيار
    $sql = "SELECT COUNT(*) as count FROM spare_parts";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['spare_parts'] = $stmt->fetch()['count'];
    
    // إجمالي الديون
    $sql = "SELECT SUM(total_debt) as total FROM customers";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['total_debt'] = $stmt->fetch()['total'] ?: 0;
    
    // إجمالي المبيعات هذا الشهر
    $sql = "SELECT SUM(paid_amount) as total FROM repair_orders 
            WHERE MONTH(created_at) = MONTH(CURRENT_DATE()) 
            AND YEAR(created_at) = YEAR(CURRENT_DATE())";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $stats['monthly_sales'] = $stmt->fetch()['total'] ?: 0;
    
    return $stats;
}

// دالة لتحديث ديون العميل
function updateCustomerDebt($customer_id) {
    global $conn;
    
    // حساب إجمالي المبالغ المتبقية
    $sql = "SELECT SUM(remaining_amount) as total_debt FROM repair_orders WHERE customer_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $total_debt = $stmt->fetch()['total_debt'] ?: 0;
    
    // تحديث ديون العميل
    $sql = "UPDATE customers SET total_debt = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$total_debt, $customer_id]);
}

// إعدادات النظام الافتراضية
$system_settings = [
    'system_name' => getSetting('system_name', 'نظام إدارة صيانة الموبايلات'),
    'center_name' => getSetting('center_name', 'مركز صيانة الموبايلات'),
    'center_address' => getSetting('center_address', 'العراق - بغداد'),
    'center_phone' => getSetting('center_phone', '07xxxxxxxxx'),
    'center_social' => getSetting('center_social', 'Facebook: @center'),
    'currency' => getSetting('currency', 'دينار عراقي'),
    'print_size' => getSetting('print_size', 'A5')
];
?>
