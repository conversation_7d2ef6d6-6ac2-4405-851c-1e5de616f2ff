<?php
require_once '../config/init.php';
checkLogin();

// الحصول على التواريخ للفلترة
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : date('Y-m-01');
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : date('Y-m-d');
$report_type = isset($_GET['report_type']) ? sanitize($_GET['report_type']) : 'summary';

// تقرير ملخص شامل
function getSummaryReport($date_from, $date_to, $conn) {
    $data = [];
    
    // إحصائيات الطلبات
    $sql = "SELECT 
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_orders,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as in_progress_orders,
        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_orders,
        SUM(repair_cost) as total_revenue,
        SUM(paid_amount) as total_paid,
        SUM(remaining_amount) as total_debt
        FROM repair_orders 
        WHERE DATE(created_at) BETWEEN ? AND ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date_from, $date_to]);
    $data['orders'] = $stmt->fetch();
    
    // إحصائيات العملاء
    $sql = "SELECT COUNT(*) as new_customers FROM customers WHERE DATE(created_at) BETWEEN ? AND ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date_from, $date_to]);
    $data['customers'] = $stmt->fetch();
    
    // إحصائيات المدفوعات
    $sql = "SELECT 
        COUNT(*) as total_payments,
        SUM(amount) as total_amount
        FROM payments 
        WHERE DATE(created_at) BETWEEN ? AND ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date_from, $date_to]);
    $data['payments'] = $stmt->fetch();
    
    return $data;
}

// تقرير يومي مفصل
function getDailyReport($date_from, $date_to, $conn) {
    $sql = "SELECT 
        DATE(created_at) as report_date,
        COUNT(*) as daily_orders,
        COUNT(CASE WHEN status = 'completed' THEN 1 END) as daily_completed,
        SUM(repair_cost) as daily_revenue,
        SUM(paid_amount) as daily_paid
        FROM repair_orders 
        WHERE DATE(created_at) BETWEEN ? AND ?
        GROUP BY DATE(created_at)
        ORDER BY report_date DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date_from, $date_to]);
    return $stmt->fetchAll();
}

// تقرير العملاء
function getCustomersReport($date_from, $date_to, $conn) {
    $sql = "SELECT 
        c.id,
        c.full_name,
        c.phone,
        COUNT(r.id) as total_orders,
        SUM(r.repair_cost) as total_spent,
        SUM(r.remaining_amount) as total_debt,
        MAX(r.created_at) as last_order_date
        FROM customers c
        LEFT JOIN repair_orders r ON c.id = r.customer_id 
        WHERE DATE(r.created_at) BETWEEN ? AND ?
        GROUP BY c.id
        ORDER BY total_spent DESC
        LIMIT 50";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$date_from, $date_to]);
    return $stmt->fetchAll();
}

// الحصول على البيانات حسب نوع التقرير
$report_data = [];
switch($report_type) {
    case 'summary':
        $report_data = getSummaryReport($date_from, $date_to, $conn);
        break;
    case 'daily':
        $report_data = getDailyReport($date_from, $date_to, $conn);
        break;
    case 'customers':
        $report_data = getCustomersReport($date_from, $date_to, $conn);
        break;
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - التقارير والإحصائيات</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="../repair/repair.css">
    <link rel="stylesheet" href="reports.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="../repair/add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="../repair/view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="reports.php" class="nav-item active">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-chart-bar"></i> التقارير والإحصائيات</h1>
                <p>تقارير شاملة ومفصلة لأداء المركز</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-success" onclick="exportReport()">
                    <i class="fas fa-download"></i> تصدير التقرير
                </button>
                <button class="btn btn-primary" onclick="printReport()">
                    <i class="fas fa-print"></i> طباعة التقرير
                </button>
            </div>
        </header>

        <!-- فلاتر التقارير -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <label>نوع التقرير</label>
                    <select name="report_type" onchange="this.form.submit()">
                        <option value="summary" <?php echo $report_type === 'summary' ? 'selected' : ''; ?>>تقرير ملخص شامل</option>
                        <option value="daily" <?php echo $report_type === 'daily' ? 'selected' : ''; ?>>تقرير يومي مفصل</option>
                        <option value="customers" <?php echo $report_type === 'customers' ? 'selected' : ''; ?>>تقرير العملاء</option>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>من تاريخ</label>
                    <input type="date" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>" onchange="this.form.submit()">
                </div>
                
                <div class="filter-group">
                    <label>إلى تاريخ</label>
                    <input type="date" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>" onchange="this.form.submit()">
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sync"></i> تحديث التقرير
                    </button>
                </div>
            </form>
        </div>

        <!-- محتوى التقرير -->
        <div class="report-container">
            <?php if ($report_type === 'summary'): ?>
            <!-- تقرير ملخص شامل -->
            <div class="report-section">
                <h3><i class="fas fa-chart-pie"></i> ملخص الفترة من <?php echo $date_from; ?> إلى <?php echo $date_to; ?></h3>
                
                <div class="summary-stats">
                    <div class="summary-card orders">
                        <div class="summary-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="summary-info">
                            <h4><?php echo number_format($report_data['orders']['total_orders']); ?></h4>
                            <p>إجمالي الطلبات</p>
                            <div class="summary-breakdown">
                                <span class="completed"><?php echo $report_data['orders']['completed_orders']; ?> مكتمل</span>
                                <span class="pending"><?php echo $report_data['orders']['pending_orders']; ?> بانتظار</span>
                                <span class="progress"><?php echo $report_data['orders']['in_progress_orders']; ?> قيد التنفيذ</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="summary-card revenue">
                        <div class="summary-icon">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="summary-info">
                            <h4><?php echo formatCurrency($report_data['orders']['total_revenue']); ?></h4>
                            <p>إجمالي الإيرادات</p>
                            <div class="summary-breakdown">
                                <span class="paid"><?php echo formatCurrency($report_data['orders']['total_paid']); ?> مدفوع</span>
                                <span class="debt"><?php echo formatCurrency($report_data['orders']['total_debt']); ?> ديون</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="summary-card customers">
                        <div class="summary-icon">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div class="summary-info">
                            <h4><?php echo number_format($report_data['customers']['new_customers']); ?></h4>
                            <p>عملاء جدد</p>
                        </div>
                    </div>
                    
                    <div class="summary-card payments">
                        <div class="summary-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="summary-info">
                            <h4><?php echo formatCurrency($report_data['payments']['total_amount']); ?></h4>
                            <p>إجمالي المدفوعات</p>
                            <div class="summary-breakdown">
                                <span><?php echo $report_data['payments']['total_payments']; ?> عملية دفع</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- الرسوم البيانية -->
                <div class="charts-container">
                    <div class="chart-card">
                        <h4>توزيع حالات الطلبات</h4>
                        <canvas id="ordersStatusChart"></canvas>
                    </div>
                    
                    <div class="chart-card">
                        <h4>الإيرادات مقابل المدفوعات</h4>
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>
            
            <?php elseif ($report_type === 'daily'): ?>
            <!-- تقرير يومي مفصل -->
            <div class="report-section">
                <h3><i class="fas fa-calendar-day"></i> التقرير اليومي المفصل</h3>
                
                <div class="table-container">
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>عدد الطلبات</th>
                                <th>طلبات مكتملة</th>
                                <th>الإيرادات</th>
                                <th>المدفوعات</th>
                                <th>معدل الإنجاز</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($report_data as $day): ?>
                            <tr>
                                <td><?php echo date('Y-m-d', strtotime($day['report_date'])); ?></td>
                                <td><?php echo number_format($day['daily_orders']); ?></td>
                                <td><?php echo number_format($day['daily_completed']); ?></td>
                                <td><?php echo formatCurrency($day['daily_revenue']); ?></td>
                                <td><?php echo formatCurrency($day['daily_paid']); ?></td>
                                <td>
                                    <?php 
                                    $completion_rate = $day['daily_orders'] > 0 ? ($day['daily_completed'] / $day['daily_orders']) * 100 : 0;
                                    echo number_format($completion_rate, 1) . '%';
                                    ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="chart-card">
                    <h4>الاتجاه اليومي للطلبات</h4>
                    <canvas id="dailyTrendChart"></canvas>
                </div>
            </div>
            
            <?php elseif ($report_type === 'customers'): ?>
            <!-- تقرير العملاء -->
            <div class="report-section">
                <h3><i class="fas fa-users"></i> تقرير أفضل العملاء</h3>
                
                <div class="table-container">
                    <table class="report-table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>عدد الطلبات</th>
                                <th>إجمالي الإنفاق</th>
                                <th>إجمالي الديون</th>
                                <th>آخر طلب</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($report_data as $customer): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($customer['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($customer['phone']); ?></td>
                                <td><?php echo number_format($customer['total_orders']); ?></td>
                                <td><?php echo formatCurrency($customer['total_spent']); ?></td>
                                <td class="<?php echo $customer['total_debt'] > 0 ? 'debt-amount' : ''; ?>">
                                    <?php echo formatCurrency($customer['total_debt']); ?>
                                </td>
                                <td><?php echo date('Y-m-d', strtotime($customer['last_order_date'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="reports.js"></script>
    
    <?php if ($report_type === 'summary'): ?>
    <script>
        // رسم بياني لحالات الطلبات
        const ordersCtx = document.getElementById('ordersStatusChart').getContext('2d');
        new Chart(ordersCtx, {
            type: 'doughnut',
            data: {
                labels: ['مكتمل', 'بانتظار', 'قيد التنفيذ', 'فشل'],
                datasets: [{
                    data: [
                        <?php echo $report_data['orders']['completed_orders']; ?>,
                        <?php echo $report_data['orders']['pending_orders']; ?>,
                        <?php echo $report_data['orders']['in_progress_orders']; ?>,
                        <?php echo $report_data['orders']['failed_orders']; ?>
                    ],
                    backgroundColor: ['#27ae60', '#f39c12', '#3498db', '#e74c3c']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // رسم بياني للإيرادات
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        new Chart(revenueCtx, {
            type: 'bar',
            data: {
                labels: ['الإيرادات الإجمالية', 'المبالغ المدفوعة', 'الديون المتبقية'],
                datasets: [{
                    data: [
                        <?php echo $report_data['orders']['total_revenue']; ?>,
                        <?php echo $report_data['orders']['total_paid']; ?>,
                        <?php echo $report_data['orders']['total_debt']; ?>
                    ],
                    backgroundColor: ['#3498db', '#27ae60', '#e74c3c']
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
    <?php endif; ?>
</body>
</html>
