<?php
// إعداد الترميز أولاً
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=UTF-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$full_name = sanitize($input['full_name'] ?? '');
$phone = sanitize($input['phone'] ?? '');
$address = sanitize($input['address'] ?? '');
$social_media = sanitize($input['social_media'] ?? '');
$notes = sanitize($input['notes'] ?? '');

// التحقق من البيانات المطلوبة
if (empty($full_name) || empty($phone)) {
    echo json_encode(['success' => false, 'message' => 'الاسم ورقم الهاتف مطلوبان'], JSON_UNESCAPED_UNICODE);
    exit();
}

// التحقق من صحة رقم الهاتف
if (strlen($phone) < 10) {
    echo json_encode(['success' => false, 'message' => 'رقم الهاتف غير صحيح'], JSON_UNESCAPED_UNICODE);
    exit();
}

// التحقق من عدم تكرار رقم الهاتف
$sql = "SELECT id FROM customers WHERE phone = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$phone]);
if ($stmt->fetch()) {
    echo json_encode(['success' => false, 'message' => 'رقم الهاتف موجود مسبقاً'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // إدراج العميل الجديد
    $sql = "INSERT INTO customers (full_name, phone, address, social_media, notes) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$full_name, $phone, $address, $social_media, $notes]);
    
    $customer_id = $conn->lastInsertId();
    
    // تسجيل العملية في السجل
    $database->logActivity('إضافة عميل', 'customers', $customer_id, null, [
        'full_name' => $full_name,
        'phone' => $phone,
        'address' => $address,
        'social_media' => $social_media,
        'notes' => $notes
    ], "تم إضافة عميل جديد: $full_name");
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم إضافة العميل بنجاح',
        'customer_id' => $customer_id
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
