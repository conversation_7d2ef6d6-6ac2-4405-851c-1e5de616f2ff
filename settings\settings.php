<?php
require_once '../config/init.php';
checkLogin();

$success_message = '';
$error_message = '';

// معالجة حفظ الإعدادات
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $settings_to_update = [
            'system_name' => sanitize($_POST['system_name']),
            'center_name' => sanitize($_POST['center_name']),
            'center_address' => sanitize($_POST['center_address']),
            'center_phone' => sanitize($_POST['center_phone']),
            'center_social' => sanitize($_POST['center_social']),
            'currency' => sanitize($_POST['currency']),
            'print_size' => sanitize($_POST['print_size']),
            'backup_frequency' => sanitize($_POST['backup_frequency']),
            'auto_backup' => isset($_POST['auto_backup']) ? '1' : '0',
            'email_notifications' => isset($_POST['email_notifications']) ? '1' : '0',
            'sms_notifications' => isset($_POST['sms_notifications']) ? '1' : '0'
        ];
        
        foreach ($settings_to_update as $key => $value) {
            $sql = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$key, $value]);
        }
        
        // تسجيل العملية في السجل
        $database->logActivity('تحديث إعدادات النظام', 'settings', null, null, $settings_to_update, "تم تحديث إعدادات النظام");
        
        $success_message = "تم حفظ الإعدادات بنجاح";
        
        // إعادة تحميل الإعدادات
        $system_settings = loadSystemSettings($conn);
        
    } catch (Exception $e) {
        $error_message = "حدث خطأ: " . $e->getMessage();
    }
}

// الحصول على إحصائيات النظام
$stats_sql = "SELECT 
    (SELECT COUNT(*) FROM customers) as total_customers,
    (SELECT COUNT(*) FROM repair_orders) as total_orders,
    (SELECT COUNT(*) FROM spare_parts) as total_parts,
    (SELECT COUNT(*) FROM documents) as total_documents,
    (SELECT COUNT(*) FROM activity_logs) as total_logs,
    (SELECT SUM(repair_cost) FROM repair_orders) as total_revenue";
$stats_stmt = $conn->prepare($stats_sql);
$stats_stmt->execute();
$system_stats = $stats_stmt->fetch();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - إعدادات النظام</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="../repair/repair.css">
    <link rel="stylesheet" href="settings.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="../repair/add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="../repair/view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="settings.php" class="nav-item active">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                <p>إدارة وتخصيص إعدادات النظام</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-success" onclick="createBackup()">
                    <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                </button>
                <button class="btn btn-warning" onclick="restoreBackup()">
                    <i class="fas fa-upload"></i> استعادة نسخة احتياطية
                </button>
            </div>
        </header>

        <?php if ($success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <!-- إحصائيات النظام -->
        <div class="system-stats">
            <h3><i class="fas fa-chart-line"></i> إحصائيات النظام</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-info">
                        <h4><?php echo number_format($system_stats['total_customers']); ?></h4>
                        <p>إجمالي العملاء</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="stat-info">
                        <h4><?php echo number_format($system_stats['total_orders']); ?></h4>
                        <p>إجمالي الطلبات</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="stat-info">
                        <h4><?php echo number_format($system_stats['total_parts']); ?></h4>
                        <p>قطع الغيار</p>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-info">
                        <h4><?php echo formatCurrency($system_stats['total_revenue']); ?></h4>
                        <p>إجمالي الإيرادات</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج الإعدادات -->
        <div class="settings-container">
            <form method="POST" class="settings-form">
                <!-- إعدادات عامة -->
                <div class="settings-section">
                    <h3><i class="fas fa-info-circle"></i> الإعدادات العامة</h3>
                    
                    <div class="form-group">
                        <label for="system_name">اسم النظام</label>
                        <input type="text" id="system_name" name="system_name" 
                               value="<?php echo htmlspecialchars($system_settings['system_name']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="center_name">اسم المركز</label>
                        <input type="text" id="center_name" name="center_name" 
                               value="<?php echo htmlspecialchars($system_settings['center_name']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="center_address">عنوان المركز</label>
                        <input type="text" id="center_address" name="center_address" 
                               value="<?php echo htmlspecialchars($system_settings['center_address']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="center_phone">هاتف المركز</label>
                        <input type="tel" id="center_phone" name="center_phone" 
                               value="<?php echo htmlspecialchars($system_settings['center_phone']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="center_social">وسائل التواصل الاجتماعي</label>
                        <input type="text" id="center_social" name="center_social" 
                               value="<?php echo htmlspecialchars($system_settings['center_social']); ?>"
                               placeholder="Facebook, Instagram, إلخ">
                    </div>
                </div>

                <!-- إعدادات العملة والطباعة -->
                <div class="settings-section">
                    <h3><i class="fas fa-money-bill-wave"></i> العملة والطباعة</h3>
                    
                    <div class="form-group">
                        <label for="currency">العملة</label>
                        <select id="currency" name="currency">
                            <option value="IQD" <?php echo $system_settings['currency'] === 'IQD' ? 'selected' : ''; ?>>دينار عراقي (IQD)</option>
                            <option value="USD" <?php echo $system_settings['currency'] === 'USD' ? 'selected' : ''; ?>>دولار أمريكي (USD)</option>
                            <option value="EUR" <?php echo $system_settings['currency'] === 'EUR' ? 'selected' : ''; ?>>يورو (EUR)</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="print_size">حجم الطباعة</label>
                        <select id="print_size" name="print_size">
                            <option value="A4" <?php echo $system_settings['print_size'] === 'A4' ? 'selected' : ''; ?>>A4</option>
                            <option value="A5" <?php echo $system_settings['print_size'] === 'A5' ? 'selected' : ''; ?>>A5</option>
                            <option value="thermal" <?php echo $system_settings['print_size'] === 'thermal' ? 'selected' : ''; ?>>طابعة حرارية</option>
                        </select>
                    </div>
                </div>

                <!-- إعدادات النسخ الاحتياطي -->
                <div class="settings-section">
                    <h3><i class="fas fa-database"></i> النسخ الاحتياطي</h3>
                    
                    <div class="form-group">
                        <label for="backup_frequency">تكرار النسخ الاحتياطي</label>
                        <select id="backup_frequency" name="backup_frequency">
                            <option value="daily" <?php echo $system_settings['backup_frequency'] === 'daily' ? 'selected' : ''; ?>>يومياً</option>
                            <option value="weekly" <?php echo $system_settings['backup_frequency'] === 'weekly' ? 'selected' : ''; ?>>أسبوعياً</option>
                            <option value="monthly" <?php echo $system_settings['backup_frequency'] === 'monthly' ? 'selected' : ''; ?>>شهرياً</option>
                            <option value="manual" <?php echo $system_settings['backup_frequency'] === 'manual' ? 'selected' : ''; ?>>يدوياً فقط</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="auto_backup" 
                                   <?php echo $system_settings['auto_backup'] ? 'checked' : ''; ?>>
                            <span class="checkmark"></span>
                            تفعيل النسخ الاحتياطي التلقائي
                        </label>
                    </div>
                </div>

                <!-- إعدادات الإشعارات -->
                <div class="settings-section">
                    <h3><i class="fas fa-bell"></i> الإشعارات</h3>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="email_notifications" 
                                   <?php echo $system_settings['email_notifications'] ? 'checked' : ''; ?>>
                            <span class="checkmark"></span>
                            إشعارات البريد الإلكتروني
                        </label>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" name="sms_notifications" 
                                   <?php echo $system_settings['sms_notifications'] ? 'checked' : ''; ?>>
                            <span class="checkmark"></span>
                            إشعارات الرسائل النصية
                        </label>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ الإعدادات
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>

        <!-- إدارة قاعدة البيانات -->
        <div class="database-management">
            <h3><i class="fas fa-database"></i> إدارة قاعدة البيانات</h3>
            
            <div class="database-actions">
                <button class="btn btn-info" onclick="optimizeDatabase()">
                    <i class="fas fa-tools"></i> تحسين قاعدة البيانات
                </button>
                
                <button class="btn btn-warning" onclick="clearOldLogs()">
                    <i class="fas fa-trash"></i> مسح السجلات القديمة
                </button>
                
                <button class="btn btn-danger" onclick="resetSystem()">
                    <i class="fas fa-exclamation-triangle"></i> إعادة تعيين النظام
                </button>
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="system-info">
            <h3><i class="fas fa-info"></i> معلومات النظام</h3>
            
            <div class="info-grid">
                <div class="info-item">
                    <strong>إصدار PHP:</strong>
                    <span><?php echo phpversion(); ?></span>
                </div>
                
                <div class="info-item">
                    <strong>إصدار MySQL:</strong>
                    <span><?php echo $conn->query('SELECT VERSION()')->fetchColumn(); ?></span>
                </div>
                
                <div class="info-item">
                    <strong>مساحة قاعدة البيانات:</strong>
                    <span id="databaseSize">جاري الحساب...</span>
                </div>
                
                <div class="info-item">
                    <strong>آخر نسخة احتياطية:</strong>
                    <span id="lastBackup">غير متوفر</span>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة استعادة النسخة الاحتياطية -->
    <div id="restoreModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-upload"></i> استعادة نسخة احتياطية</h3>
                <button class="modal-close" onclick="closeRestoreModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="restoreForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="warning-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p><strong>تحذير:</strong> ستؤدي هذه العملية إلى استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="backup_file">اختر ملف النسخة الاحتياطية</label>
                        <input type="file" id="backup_file" name="backup_file" accept=".sql,.zip" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="confirm_restore" required>
                            <span class="checkmark"></span>
                            أؤكد أنني أريد استعادة النسخة الاحتياطية وأفهم أن هذا سيحذف البيانات الحالية
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-upload"></i> استعادة النسخة الاحتياطية
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeRestoreModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="settings.js"></script>
</body>
</html>
