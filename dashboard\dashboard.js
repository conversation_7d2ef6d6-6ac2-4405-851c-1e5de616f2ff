// ملف JavaScript للوحة التحكم

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة العناصر
    initializeDashboard();
    
    // تأثيرات بصرية للبطاقات
    animateStatCards();
    
    // تحديث الوقت
    updateTime();
    setInterval(updateTime, 1000);
});

// دالة تهيئة لوحة التحكم
function initializeDashboard() {
    // تأثير hover للبطاقات
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // تأثير للأزرار السريعة
    const actionBtns = document.querySelectorAll('.action-btn');
    actionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            const icon = this.querySelector('i');
            icon.style.transform = 'scale(1.2) rotate(10deg)';
        });
        
        btn.addEventListener('mouseleave', function() {
            const icon = this.querySelector('i');
            icon.style.transform = 'scale(1) rotate(0deg)';
        });
    });

    // تأثير للشريط الجانبي
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.addEventListener('click', function() {
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// دالة تبديل الشريط الجانبي
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    sidebar.classList.toggle('collapsed');
    
    // تأثير انتقالي سلس
    setTimeout(() => {
        if (sidebar.classList.contains('collapsed')) {
            mainContent.style.marginRight = '70px';
        } else {
            mainContent.style.marginRight = '280px';
        }
    }, 150);
}

// دالة تحريك بطاقات الإحصائيات
function animateStatCards() {
    const cards = document.querySelectorAll('.stat-card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// دالة فتح نتيجة البحث
function openResult(type, id) {
    let url = '';
    
    switch(type) {
        case 'customer':
            url = `../customers/view_customer.php?id=${id}`;
            break;
        case 'order':
            url = `../repair/view_repair.php?id=${id}`;
            break;
        case 'part':
            url = `../parts/view_part.php?id=${id}`;
            break;
        default:
            return;
    }
    
    // تأثير انتقالي قبل التوجيه
    const resultCard = event.currentTarget;
    resultCard.style.transform = 'scale(0.95)';
    resultCard.style.opacity = '0.7';
    
    setTimeout(() => {
        window.location.href = url;
    }, 200);
}

// دالة تحديث الوقت
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-EG', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
    
    const dateString = now.toLocaleDateString('ar-EG', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    // إضافة الوقت إلى الشريط العلوي إذا لم يكن موجوداً
    let timeDisplay = document.getElementById('timeDisplay');
    if (!timeDisplay) {
        timeDisplay = document.createElement('div');
        timeDisplay.id = 'timeDisplay';
        timeDisplay.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            font-size: 12px;
            color: #2c3e50;
            z-index: 1001;
            backdrop-filter: blur(10px);
        `;
        document.body.appendChild(timeDisplay);
    }
    
    timeDisplay.innerHTML = `
        <div style="font-weight: 600;">${timeString}</div>
        <div style="font-size: 10px; color: #7f8c8d;">${dateString}</div>
    `;
}

// دالة البحث المباشر
function liveSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                performLiveSearch(query);
            }, 500);
        } else {
            clearSearchResults();
        }
    });
}

// دالة تنفيذ البحث المباشر
function performLiveSearch(query) {
    // إظهار مؤشر التحميل
    showSearchLoading();
    
    // محاكاة استدعاء AJAX
    fetch(`search.php?q=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            displaySearchResults(data);
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
            hideSearchLoading();
        });
}

// دالة إظهار مؤشر تحميل البحث
function showSearchLoading() {
    const searchForm = document.querySelector('.search-form');
    const button = searchForm.querySelector('button');
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
}

// دالة إخفاء مؤشر تحميل البحث
function hideSearchLoading() {
    const searchForm = document.querySelector('.search-form');
    const button = searchForm.querySelector('button');
    
    button.innerHTML = '<i class="fas fa-search"></i>';
}

// دالة عرض نتائج البحث
function displaySearchResults(results) {
    hideSearchLoading();
    
    // إنشاء أو تحديث منطقة النتائج
    let resultsContainer = document.getElementById('liveSearchResults');
    if (!resultsContainer) {
        resultsContainer = document.createElement('div');
        resultsContainer.id = 'liveSearchResults';
        resultsContainer.style.cssText = `
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            margin-top: 5px;
        `;
        document.querySelector('.search-container').appendChild(resultsContainer);
    }
    
    if (results.length === 0) {
        resultsContainer.innerHTML = '<div style="padding: 15px; text-align: center; color: #7f8c8d;">لا توجد نتائج</div>';
        return;
    }
    
    let html = '';
    results.forEach(result => {
        html += `
            <div class="live-result-item" onclick="openResult('${result.type}', ${result.id})" style="
                padding: 12px 15px;
                border-bottom: 1px solid #f1f2f6;
                cursor: pointer;
                transition: background 0.2s ease;
            " onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='white'">
                <div style="font-weight: 600; color: #2c3e50; margin-bottom: 3px;">${result.title}</div>
                <div style="font-size: 12px; color: #7f8c8d;">${result.subtitle}</div>
            </div>
        `;
    });
    
    resultsContainer.innerHTML = html;
}

// دالة مسح نتائج البحث
function clearSearchResults() {
    const resultsContainer = document.getElementById('liveSearchResults');
    if (resultsContainer) {
        resultsContainer.remove();
    }
}

// دالة تحديث الإحصائيات تلقائياً
function autoUpdateStats() {
    setInterval(() => {
        fetch('get_stats.php')
            .then(response => response.json())
            .then(stats => {
                updateStatCards(stats);
            })
            .catch(error => {
                console.error('خطأ في تحديث الإحصائيات:', error);
            });
    }, 30000); // تحديث كل 30 ثانية
}

// دالة تحديث بطاقات الإحصائيات
function updateStatCards(stats) {
    const cards = document.querySelectorAll('.stat-card');
    
    cards.forEach(card => {
        const h3 = card.querySelector('h3');
        const currentValue = parseInt(h3.textContent.replace(/[^\d]/g, ''));
        
        // تحديد النوع والقيمة الجديدة
        let newValue = 0;
        if (card.classList.contains('customers')) newValue = stats.customers;
        else if (card.classList.contains('orders')) newValue = stats.orders;
        else if (card.classList.contains('completed')) newValue = stats.completed_orders;
        else if (card.classList.contains('parts')) newValue = stats.spare_parts;
        
        // تأثير العد التصاعدي
        if (newValue !== currentValue) {
            animateNumber(h3, currentValue, newValue);
        }
    });
}

// دالة تحريك الأرقام
function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString('ar-EG');
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

// تهيئة البحث المباشر عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    liveSearch();
    autoUpdateStats();
});

// إضافة مستمع للنقر خارج نتائج البحث لإخفائها
document.addEventListener('click', function(event) {
    const searchContainer = document.querySelector('.search-container');
    const resultsContainer = document.getElementById('liveSearchResults');
    
    if (resultsContainer && !searchContainer.contains(event.target)) {
        clearSearchResults();
    }
});
