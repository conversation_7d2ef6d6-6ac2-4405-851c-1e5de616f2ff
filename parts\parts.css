/* ملف CSS لإدارة قطع الغيار */

/* إحصائيات قطع الغيار */
.stat-card.quantity .stat-icon { 
    background: linear-gradient(135deg, #f39c12, #e67e22); 
}

.stat-card.purchase .stat-icon { 
    background: linear-gradient(135deg, #e74c3c, #c0392b); 
}

.stat-card.selling .stat-icon { 
    background: linear-gradient(135deg, #27ae60, #229954); 
}

/* جدول قطع الغيار */
.parts-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;
}

.parts-table thead {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.parts-table th {
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
}

.parts-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
}

.parts-table tbody tr {
    transition: all 0.3s ease;
}

.parts-table tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
}

/* تنسيق الأسعار */
.price {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 5px;
    font-size: 13px;
}

.purchase-price {
    background: #ffebee;
    color: #c62828;
}

.selling-price {
    background: #e8f5e8;
    color: #2e7d32;
}

/* تنسيق الكمية */
.quantity {
    font-weight: 700;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 14px;
}

.quantity-low {
    background: #ffebee;
    color: #c62828;
    border: 2px solid #ef5350;
}

.quantity-medium {
    background: #fff3e0;
    color: #ef6c00;
    border: 2px solid #ff9800;
}

.quantity-high {
    background: #e8f5e8;
    color: #2e7d32;
    border: 2px solid #4caf50;
}

/* تنسيق الربح */
.profit {
    font-weight: 600;
}

.profit.positive {
    color: #27ae60;
}

.profit.negative {
    color: #e74c3c;
}

.profit small {
    display: block;
    font-size: 11px;
    opacity: 0.8;
}

/* حالة المخزون */
.stock-status {
    padding: 4px 8px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-low {
    background: #ffebee;
    color: #c62828;
    border: 1px solid #ef5350;
}

.status-medium {
    background: #fff3e0;
    color: #ef6c00;
    border: 1px solid #ff9800;
}

.status-high {
    background: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #4caf50;
}

/* الباركود */
.barcode {
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    border: 1px solid #dee2e6;
    color: #495057;
}

/* أزرار الإجراءات الخاصة */
.btn-stock {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

/* نموذج القطعة */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.profit-preview {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    padding: 15px;
    border-radius: 8px;
    border: 2px solid #4caf50;
    margin-top: 15px;
}

.profit-info {
    text-align: center;
    font-weight: 600;
    color: #2e7d32;
}

.profit-amount {
    font-size: 18px;
    margin: 0 10px;
}

.profit-percentage {
    font-size: 14px;
    opacity: 0.8;
}

/* نافذة تحديث المخزون */
.stock-info {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
    border: 2px solid #2196f3;
}

.stock-info h4 {
    color: #1565c0;
    font-size: 18px;
    margin-bottom: 8px;
}

.current-stock {
    font-size: 20px;
    font-weight: 700;
    color: #1976d2;
}

.new-quantity-preview {
    background: #fff3e0;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ff9800;
    margin-top: 10px;
    text-align: center;
}

.new-quantity {
    font-weight: 700;
    color: #ef6c00;
}

/* تعليمات الاستيراد */
.import-instructions {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #2196f3;
}

.import-instructions h4 {
    color: #1565c0;
    margin-bottom: 10px;
}

.import-instructions ul {
    margin: 0;
    padding-right: 20px;
}

.import-instructions li {
    margin-bottom: 5px;
    color: #424242;
}

/* تحسينات للأزرار */
.header-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

/* تأثيرات خاصة */
.parts-table tbody tr {
    position: relative;
}

.parts-table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    transition: all 0.3s ease;
}

.parts-table tbody tr:hover::before {
    background: linear-gradient(180deg, #3498db, #2980b9);
}

/* تنسيق خاص للقطع منخفضة المخزون */
.parts-table tbody tr.low-stock {
    background: rgba(255, 235, 238, 0.3);
}

.parts-table tbody tr.low-stock::before {
    background: linear-gradient(180deg, #e74c3c, #c0392b);
}

/* تحسينات للنوافذ المنبثقة */
.modal-content {
    max-width: 700px;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .header-actions {
        flex-direction: column;
        width: 100%;
    }
    
    .header-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .parts-table {
        font-size: 12px;
    }
    
    .parts-table th,
    .parts-table td {
        padding: 8px 6px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }
    
    .btn-action {
        width: 100%;
        height: 30px;
        font-size: 12px;
    }
}

/* تحسينات للطباعة */
@media print {
    .header-actions,
    .filters-container,
    .action-buttons,
    .pagination {
        display: none !important;
    }
    
    .parts-table {
        font-size: 10px;
    }
    
    .parts-table th,
    .parts-table td {
        padding: 4px;
        border: 1px solid #000 !important;
    }
}

/* تأثيرات التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.loading-spinner i {
    font-size: 30px;
    color: #3498db;
    margin-bottom: 15px;
}

/* تحسينات للنصوص العربية */
* {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

input, textarea, select {
    direction: rtl !important;
    text-align: right !important;
}

input::placeholder,
textarea::placeholder {
    direction: rtl !important;
    text-align: right !important;
    opacity: 0.7;
}

/* تنسيق خاص للأرقام */
.price, .quantity, .profit {
    direction: ltr;
    text-align: left;
    font-family: 'Cairo', sans-serif !important;
}

/* تحسينات إضافية */
.btn-action.btn-stock:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
}

.parts-table tbody tr.out-of-stock {
    opacity: 0.6;
    background: #ffebee;
}

.parts-table tbody tr.out-of-stock .quantity {
    background: #f44336;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}
