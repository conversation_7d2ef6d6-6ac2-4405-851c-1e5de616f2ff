/* ملف CSS لنظام الصيانة */

.page-header {
    background: white;
    padding: 25px 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.header-content h1 i {
    color: #3498db;
    margin-left: 10px;
}

.header-content p {
    color: #7f8c8d;
    font-size: 14px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* الأزرار */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(149, 165, 166, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* التنبيهات */
.alert {
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.alert-success {
    background: linear-gradient(135deg, #d5f4e6, #a8e6cf);
    color: #27ae60;
    border: 1px solid #27ae60;
}

.alert-error {
    background: linear-gradient(135deg, #ffeaa7, #fab1a0);
    color: #e74c3c;
    border: 1px solid #e74c3c;
}

/* حاوية النموذج */
.form-container {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
    margin-bottom: 30px;
}

.form-section {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-section:hover {
    border-color: #3498db;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.1);
}

.form-section.full-width {
    grid-column: 1 / -1;
}

.form-section h3 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

.form-section h3 i {
    color: #3498db;
    margin-left: 8px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-1px);
}

.form-group textarea {
    min-height: 100px;
    resize: vertical;
}

/* اختيار العميل */
.customer-select-container {
    display: flex;
    gap: 10px;
    align-items: stretch;
}

.customer-select-container select {
    flex: 1;
}

.btn-add-customer {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    border: none;
    padding: 12px 15px;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    font-size: 12px;
    white-space: nowrap;
    transition: all 0.3s ease;
}

.btn-add-customer:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

/* إجراءات النموذج */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding-top: 20px;
    border-top: 2px solid #e9ecef;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: #2c3e50;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.modal-header h3 i {
    color: #3498db;
    margin-left: 8px;
}

.modal-close {
    background: none;
    border: none;
    color: #7f8c8d;
    font-size: 20px;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #e74c3c;
    color: white;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 2px solid #e9ecef;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* حالات الطلبات */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.status-in-progress {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.status-completed {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.status-failed {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.status-delivered {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .customer-select-container {
        flex-direction: column;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
    }
}

/* تأثيرات إضافية */
.form-section {
    position: relative;
    overflow: hidden;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #3498db, #2980b9);
    transition: all 0.3s ease;
}

.form-section:hover::before {
    width: 8px;
}

/* أدوات البحث والفلترة */
.filters-container {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.filters-form {
    display: flex;
    gap: 15px;
    align-items: end;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
    min-width: 200px;
}

.filter-group input,
.filter-group select {
    padding: 10px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* إحصائيات سريعة */
.quick-stats {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
}

.stat-item i {
    font-size: 18px;
}

/* حاوية الجدول */
.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* الجدول */
.orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.orders-table thead {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.orders-table th {
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
}

.orders-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
}

.orders-table tbody tr {
    transition: all 0.3s ease;
}

.orders-table tbody tr:hover {
    background: #f8f9fa;
}

/* أزرار الإجراءات */
.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-action {
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-view {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-edit {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.btn-print {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.btn-status {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
}

.btn-delete {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-action:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* معاينة المشكلة */
.problem-preview {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* رسالة عدم وجود بيانات */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.no-data i {
    font-size: 64px;
    margin-bottom: 20px;
    color: #bdc3c7;
}

.no-data h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: #2c3e50;
}

/* ترقيم الصفحات */
.pagination {
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #e9ecef;
}

.page-btn {
    padding: 10px 15px;
    background: white;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    color: #2c3e50;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.page-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.page-btn.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .page-header,
    .form-actions,
    .btn,
    .filters-container,
    .pagination {
        display: none !important;
    }

    .main-content {
        margin: 0 !important;
        padding: 0 !important;
    }

    .form-container,
    .table-container {
        box-shadow: none !important;
        border: 1px solid #000 !important;
    }
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .filters-form {
        flex-direction: column;
    }

    .filter-group {
        min-width: 100%;
    }

    .orders-table {
        font-size: 12px;
    }

    .orders-table th,
    .orders-table td {
        padding: 8px 6px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .btn-action {
        width: 100%;
        height: 30px;
    }
}
