// ملف JavaScript لصفحة تسجيل الدخول

document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginBtn = document.querySelector('.login-btn');

    // تأثير التركيز على الحقول
    const inputWrappers = document.querySelectorAll('.input-wrapper');
    inputWrappers.forEach(wrapper => {
        const input = wrapper.querySelector('input');
        
        input.addEventListener('focus', function() {
            wrapper.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            wrapper.style.transform = 'scale(1)';
        });
    });

    // تأثير الضغط على زر تسجيل الدخول
    loginBtn.addEventListener('mousedown', function() {
        this.style.transform = 'scale(0.98)';
    });

    loginBtn.addEventListener('mouseup', function() {
        this.style.transform = 'scale(1)';
    });

    // التحقق من صحة البيانات قبل الإرسال
    loginForm.addEventListener('submit', function(e) {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();

        if (!username || !password) {
            e.preventDefault();
            showError('يرجى ملء جميع الحقول');
            return;
        }

        if (username.length < 2) {
            e.preventDefault();
            showError('اسم المستخدم قصير جداً');
            return;
        }

        if (password.length < 4) {
            e.preventDefault();
            showError('كلمة المرور قصيرة جداً');
            return;
        }

        // إظهار مؤشر التحميل
        showLoading();
    });

    // دالة إظهار رسالة خطأ
    function showError(message) {
        // إزالة رسالة الخطأ السابقة إن وجدت
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }

        // إنشاء رسالة خطأ جديدة
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
        
        // إدراج رسالة الخطأ قبل النموذج
        loginForm.insertBefore(errorDiv, loginForm.firstChild);

        // إزالة رسالة الخطأ بعد 5 ثوان
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);

        // تأثير اهتزاز للنموذج
        loginForm.style.animation = 'shake 0.5s ease-in-out';
        setTimeout(() => {
            loginForm.style.animation = '';
        }, 500);
    }

    // دالة إظهار مؤشر التحميل
    function showLoading() {
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
        loginBtn.disabled = true;
        loginBtn.style.opacity = '0.8';
    }

    // تأثير الكتابة على لوحة المفاتيح
    usernameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            passwordInput.focus();
        }
    });

    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginForm.submit();
        }
    });

    // تأثيرات بصرية إضافية
    const shapes = document.querySelectorAll('.shape');
    shapes.forEach((shape, index) => {
        shape.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
            this.style.opacity = '0.3';
        });
        
        shape.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.opacity = '0.1';
        });
    });

    // تأثير الماوس على البطاقة
    const loginCard = document.querySelector('.login-card');
    loginCard.addEventListener('mousemove', function(e) {
        const rect = this.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        const centerX = rect.width / 2;
        const centerY = rect.height / 2;
        
        const rotateX = (y - centerY) / 20;
        const rotateY = (centerX - x) / 20;
        
        this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`;
    });

    loginCard.addEventListener('mouseleave', function() {
        this.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg)';
    });
});

// دالة إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleIcon.className = 'fas fa-eye';
    }
}

// إضافة تأثير اهتزاز للأخطاء
const shakeKeyframes = `
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}`;

// إضافة الـ CSS للتأثيرات
const style = document.createElement('style');
style.textContent = shakeKeyframes;
document.head.appendChild(style);
