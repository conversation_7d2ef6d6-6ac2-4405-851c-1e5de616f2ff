// ملف JavaScript لإدارة المستندات

document.addEventListener('DOMContentLoaded', function() {
    initializeDocuments();
    setupDocumentModals();
    animateDocumentsTable();
    highlightRecentDocuments();
});

// تهيئة صفحة المستندات
function initializeDocuments() {
    // إعداد البحث المباشر
    setupLiveSearch();
    
    // إعداد فلاتر التاريخ
    setupDateFilters();
    
    // إعداد نموذج الإيميل
    setupEmailForm();
}

// تحريك جدول المستندات
function animateDocumentsTable() {
    const rows = document.querySelectorAll('.documents-table tbody tr');
    
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateX(20px)';
        
        setTimeout(() => {
            row.style.transition = 'all 0.4s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateX(0)';
        }, index * 50);
    });
}

// تمييز المستندات الحديثة
function highlightRecentDocuments() {
    const rows = document.querySelectorAll('.documents-table tbody tr');
    const today = new Date().toISOString().split('T')[0];
    
    rows.forEach(row => {
        const dateCell = row.querySelector('.document-date');
        if (dateCell) {
            const documentDate = dateCell.textContent.trim().split('\n')[0];
            if (documentDate === today) {
                row.classList.add('recent');
            }
        }
    });
}

// إعداد النوافذ المنبثقة
function setupDocumentModals() {
    const emailForm = document.getElementById('emailForm');
    
    if (emailForm) {
        emailForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitEmailForm();
        });
    }
}

// عرض المستند
function viewDocument(documentId) {
    document.getElementById('documentModalTitle').innerHTML = '<i class="fas fa-file-alt"></i> عرض المستند';
    document.getElementById('documentContent').innerHTML = '<div class="document-loading"><i class="fas fa-spinner fa-spin"></i><p>جاري تحميل المستند...</p></div>';
    document.getElementById('documentModal').classList.add('show');
    
    // جلب محتوى المستند
    fetch(`get_document.php?id=${documentId}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('documentContent').innerHTML = html;
        })
        .catch(error => {
            console.error('خطأ:', error);
            document.getElementById('documentContent').innerHTML = '<div class="alert alert-error">حدث خطأ في تحميل المستند</div>';
        });
}

// طباعة المستند
function printDocument(documentId, documentType) {
    let printUrl = '';
    
    switch(documentType) {
        case 'repair_receipt':
            printUrl = `../repair/print_receipt.php?id=${documentId}`;
            break;
        case 'payment_receipt':
            printUrl = `../customers/print_payment_receipt.php?id=${documentId}`;
            break;
        default:
            alert('نوع المستند غير مدعوم للطباعة');
            return;
    }
    
    window.open(printUrl, '_blank', 'width=800,height=600');
}

// تحميل المستند كـ PDF
function downloadDocument(documentId) {
    const button = event.currentTarget;
    const originalIcon = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;
    
    fetch(`download_document.php?id=${documentId}`)
        .then(response => {
            if (response.ok) {
                return response.blob();
            }
            throw new Error('فشل في تحميل المستند');
        })
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `document_${documentId}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            showSuccessMessage('تم تحميل المستند بنجاح');
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في تحميل المستند');
        })
        .finally(() => {
            button.innerHTML = originalIcon;
            button.disabled = false;
        });
}

// إرسال المستند بالإيميل
function emailDocument(documentId) {
    document.getElementById('email_document_id').value = documentId;
    document.getElementById('emailForm').reset();
    document.getElementById('email_document_id').value = documentId;
    
    // تعيين موضوع افتراضي
    document.getElementById('email_subject').value = 'مستند من ' + document.querySelector('h3').textContent;
    
    document.getElementById('emailModal').classList.add('show');
    
    setTimeout(() => {
        document.getElementById('recipient_email').focus();
    }, 300);
}

// حذف المستند
function deleteDocument(documentId) {
    if (confirm('هل أنت متأكد من حذف هذا المستند؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        const button = event.currentTarget;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        fetch('delete_document.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ document_id: documentId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const row = button.closest('tr');
                row.style.transition = 'all 0.5s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    row.remove();
                    showSuccessMessage('تم حذف المستند بنجاح');
                }, 500);
            } else {
                alert('حدث خطأ: ' + data.message);
                button.innerHTML = originalIcon;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
            button.innerHTML = originalIcon;
            button.disabled = false;
        });
    }
}

// تصدير المستندات
function exportDocuments() {
    const filters = {
        search: document.querySelector('input[name="search"]').value,
        type: document.querySelector('select[name="type"]').value,
        date_from: document.querySelector('input[name="date_from"]').value,
        date_to: document.querySelector('input[name="date_to"]').value
    };
    
    showLoadingOverlay('جاري تصدير المستندات...');
    
    fetch('export_documents.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => response.blob())
    .then(blob => {
        hideLoadingOverlay();
        
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `documents_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        showSuccessMessage('تم تصدير المستندات بنجاح');
    })
    .catch(error => {
        hideLoadingOverlay();
        console.error('خطأ:', error);
        alert('حدث خطأ في التصدير');
    });
}

// طباعة مجمعة للمستندات
function printAllDocuments() {
    const selectedDocuments = [];
    const checkboxes = document.querySelectorAll('.document-checkbox:checked');
    
    if (checkboxes.length === 0) {
        alert('يرجى اختيار المستندات المراد طباعتها');
        return;
    }
    
    checkboxes.forEach(checkbox => {
        selectedDocuments.push({
            id: checkbox.value,
            type: checkbox.dataset.type
        });
    });
    
    // فتح نافذة طباعة مجمعة
    const printWindow = window.open('print_multiple.php', '_blank', 'width=1000,height=700');
    
    // إرسال قائمة المستندات للطباعة
    printWindow.addEventListener('load', function() {
        printWindow.postMessage({
            action: 'print_documents',
            documents: selectedDocuments
        }, '*');
    });
}

// إغلاق النوافذ
function closeDocumentModal() {
    document.getElementById('documentModal').classList.remove('show');
}

function closeEmailModal() {
    document.getElementById('emailModal').classList.remove('show');
}

// طباعة المستند الحالي
function printCurrentDocument() {
    const documentContent = document.getElementById('documentContent');
    const printWindow = window.open('', '_blank');
    
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>طباعة المستند</title>
            <style>
                body { font-family: 'Cairo', sans-serif; direction: rtl; }
                @media print { body { margin: 0; } }
            </style>
        </head>
        <body>
            ${documentContent.innerHTML}
        </body>
        </html>
    `);
    
    printWindow.document.close();
    printWindow.print();
}

// إعداد البحث المباشر
function setupLiveSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
    }
}

// إعداد فلاتر التاريخ
function setupDateFilters() {
    const dateFromInput = document.querySelector('input[name="date_from"]');
    const dateToInput = document.querySelector('input[name="date_to"]');
    
    if (dateFromInput && dateToInput) {
        dateFromInput.addEventListener('change', function() {
            if (this.value && dateToInput.value && this.value > dateToInput.value) {
                dateToInput.value = this.value;
            }
        });
        
        dateToInput.addEventListener('change', function() {
            if (this.value && dateFromInput.value && this.value < dateFromInput.value) {
                dateFromInput.value = this.value;
            }
        });
    }
}

// إعداد نموذج الإيميل
function setupEmailForm() {
    const recipientEmail = document.getElementById('recipient_email');
    
    if (recipientEmail) {
        recipientEmail.addEventListener('blur', function() {
            if (this.value && !isValidEmail(this.value)) {
                this.style.borderColor = '#e74c3c';
                showTooltip(this, 'البريد الإلكتروني غير صحيح');
            } else {
                this.style.borderColor = '#27ae60';
                hideTooltip(this);
            }
        });
    }
}

// إرسال نموذج الإيميل
function submitEmailForm() {
    const formData = {
        document_id: document.getElementById('email_document_id').value,
        recipient_email: document.getElementById('recipient_email').value.trim(),
        subject: document.getElementById('email_subject').value.trim(),
        message: document.getElementById('email_message').value.trim()
    };
    
    if (!formData.recipient_email || !isValidEmail(formData.recipient_email)) {
        alert('يرجى إدخال بريد إلكتروني صحيح');
        return;
    }
    
    const submitBtn = document.querySelector('#emailForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الإرسال...';
    submitBtn.disabled = true;
    
    fetch('send_document_email.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeEmailModal();
            showSuccessMessage('تم إرسال المستند بالإيميل بنجاح');
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الإرسال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// إظهار تلميح
function showTooltip(element, message) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip';
    tooltip.textContent = message;
    tooltip.style.cssText = `
        position: absolute;
        background: #e74c3c;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 12px;
        z-index: 1000;
        top: 100%;
        left: 0;
        margin-top: 5px;
    `;
    
    element.parentNode.style.position = 'relative';
    element.parentNode.appendChild(tooltip);
}

// إخفاء التلميح
function hideTooltip(element) {
    const tooltip = element.parentNode.querySelector('.tooltip');
    if (tooltip) {
        tooltip.remove();
    }
}

// إظهار شاشة التحميل
function showLoadingOverlay(message) {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>${message}</p>
        </div>
    `;
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    `;
    document.body.appendChild(overlay);
}

// إخفاء شاشة التحميل
function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success';
    successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    successDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        padding: 15px;
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
        border-radius: 8px;
        animation: slideInRight 0.5s ease-out;
    `;
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
        successDiv.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => successDiv.remove(), 500);
    }, 4000);
}

// إغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    const documentModal = document.getElementById('documentModal');
    const emailModal = document.getElementById('emailModal');
    
    if (e.target === documentModal) closeDocumentModal();
    if (e.target === emailModal) closeEmailModal();
});

// إغلاق النوافذ بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDocumentModal();
        closeEmailModal();
    }
});

// إضافة تأثيرات CSS للحركة
const style = document.createElement('style');
style.textContent = `
@keyframes slideInRight {
    from { opacity: 0; transform: translateX(100%); }
    to { opacity: 1; transform: translateX(0); }
}

@keyframes slideOutRight {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(100%); }
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.loading-spinner i {
    font-size: 30px;
    color: #3498db;
    margin-bottom: 15px;
}
`;
document.head.appendChild(style);
