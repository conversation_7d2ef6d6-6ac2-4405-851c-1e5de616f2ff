<?php
require_once '../config/init.php';
checkLogin();

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(r.order_number LIKE ? OR c.full_name LIKE ? OR c.phone LIKE ? OR r.problem_description LIKE ? OR r.barcode LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term, $search_term]);
}

if (!empty($status_filter)) {
    $where_conditions[] = "r.status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد الطلبات
$count_sql = "SELECT COUNT(*) as total FROM repair_orders r 
               JOIN customers c ON r.customer_id = c.id 
               JOIN phone_brands b ON r.phone_brand_id = b.id 
               $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_orders = $count_stmt->fetch()['total'];
$total_pages = ceil($total_orders / $per_page);

// الحصول على الطلبات
$sql = "SELECT r.*, c.full_name, c.phone, b.brand_name 
        FROM repair_orders r 
        JOIN customers c ON r.customer_id = c.id 
        JOIN phone_brands b ON r.phone_brand_id = b.id 
        $where_clause 
        ORDER BY r.created_at DESC 
        LIMIT $per_page OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$orders = $stmt->fetchAll();

// دالة لتحديد لون الحالة
function getStatusColor($status) {
    switch($status) {
        case 'pending': return '#f39c12';
        case 'in_progress': return '#3498db';
        case 'completed': return '#27ae60';
        case 'failed': return '#e74c3c';
        case 'waiting_parts': return '#9b59b6';
        case 'delivered': return '#2c3e50';
        default: return '#95a5a6';
    }
}

// دالة لترجمة الحالة
function translateStatus($status) {
    switch($status) {
        case 'pending': return 'بانتظار البدء';
        case 'in_progress': return 'قيد الصيانة';
        case 'completed': return 'مكتمل';
        case 'failed': return 'فشل الصيانة';
        case 'waiting_parts': return 'بانتظار قطع الغيار';
        case 'delivered': return 'تم التسليم';
        default: return 'غير محدد';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - عرض الطلبات</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="repair.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="view_repairs.php" class="nav-item active">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-list"></i> عرض طلبات الصيانة</h1>
                <p>إدارة ومتابعة جميع طلبات الصيانة</p>
            </div>
            <div class="header-actions">
                <a href="add_repair.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة طلب جديد
                </a>
            </div>
        </header>

        <!-- أدوات البحث والفلترة -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="البحث في الطلبات..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="filter-group">
                    <select name="status">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>بانتظار البدء</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>قيد الصيانة</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                        <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>فشل الصيانة</option>
                        <option value="waiting_parts" <?php echo $status_filter === 'waiting_parts' ? 'selected' : ''; ?>>بانتظار قطع الغيار</option>
                        <option value="delivered" <?php echo $status_filter === 'delivered' ? 'selected' : ''; ?>>تم التسليم</option>
                    </select>
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="view_repairs.php" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <div class="stat-item">
                <i class="fas fa-clipboard-list"></i>
                <span>إجمالي الطلبات: <?php echo $total_orders; ?></span>
            </div>
        </div>

        <!-- جدول الطلبات -->
        <div class="table-container">
            <?php if (empty($orders)): ?>
            <div class="no-data">
                <i class="fas fa-inbox"></i>
                <h3>لا توجد طلبات</h3>
                <p>لم يتم العثور على أي طلبات تطابق معايير البحث</p>
                <a href="add_repair.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة طلب جديد
                </a>
            </div>
            <?php else: ?>
            <table class="orders-table">
                <thead>
                    <tr>
                        <th>رقم الطلب</th>
                        <th>العميل</th>
                        <th>نوع الجهاز</th>
                        <th>المشكلة</th>
                        <th>التكلفة</th>
                        <th>الحالة</th>
                        <th>التاريخ</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($orders as $order): ?>
                    <tr>
                        <td>
                            <strong style="color: #e74c3c;"><?php echo $order['order_number']; ?></strong>
                            <br>
                            <small style="color: #7f8c8d;"><?php echo $order['barcode']; ?></small>
                        </td>
                        <td>
                            <strong><?php echo htmlspecialchars($order['full_name']); ?></strong>
                            <br>
                            <small><?php echo htmlspecialchars($order['phone']); ?></small>
                        </td>
                        <td>
                            <strong><?php echo htmlspecialchars($order['brand_name']); ?></strong>
                            <br>
                            <small><?php echo htmlspecialchars($order['phone_model']); ?></small>
                        </td>
                        <td>
                            <div class="problem-preview">
                                <?php echo htmlspecialchars(substr($order['problem_description'], 0, 50)) . '...'; ?>
                            </div>
                        </td>
                        <td>
                            <strong><?php echo formatCurrency($order['repair_cost']); ?></strong>
                            <?php if ($order['remaining_amount'] > 0): ?>
                            <br>
                            <small style="color: #e74c3c;">متبقي: <?php echo formatCurrency($order['remaining_amount']); ?></small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="status-badge" style="background-color: <?php echo getStatusColor($order['status']); ?>">
                                <?php echo translateStatus($order['status']); ?>
                            </span>
                        </td>
                        <td>
                            <?php echo date('Y-m-d', strtotime($order['created_at'])); ?>
                            <br>
                            <small><?php echo date('H:i', strtotime($order['created_at'])); ?></small>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn-action btn-view" onclick="viewOrder(<?php echo $order['id']; ?>)" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn-action btn-edit" onclick="editOrder(<?php echo $order['id']; ?>)" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-action btn-print" onclick="printReceipt(<?php echo $order['id']; ?>)" title="طباعة الإيصال">
                                    <i class="fas fa-print"></i>
                                </button>
                                <button class="btn-action btn-status" onclick="updateStatus(<?php echo $order['id']; ?>)" title="تحديث الحالة">
                                    <i class="fas fa-sync"></i>
                                </button>
                                <button class="btn-action btn-delete" onclick="deleteOrder(<?php echo $order['id']; ?>)" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>

            <!-- ترقيم الصفحات -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" class="page-btn">
                    <i class="fas fa-chevron-right"></i> السابق
                </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" 
                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                    <?php echo $i; ?>
                </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&status=<?php echo urlencode($status_filter); ?>" class="page-btn">
                    التالي <i class="fas fa-chevron-left"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- نافذة تحديث الحالة -->
    <div id="statusModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-sync"></i> تحديث حالة الطلب</h3>
                <button class="modal-close" onclick="closeStatusModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="statusForm">
                <div class="modal-body">
                    <input type="hidden" id="status_order_id">
                    
                    <div class="form-group">
                        <label for="new_status">الحالة الجديدة</label>
                        <select id="new_status" required onchange="toggleStatusFields()">
                            <option value="">اختر الحالة</option>
                            <option value="pending">بانتظار البدء</option>
                            <option value="in_progress">قيد الصيانة</option>
                            <option value="completed">مكتمل</option>
                            <option value="failed">فشل الصيانة</option>
                            <option value="waiting_parts">بانتظار قطع الغيار</option>
                            <option value="delivered">تم التسليم</option>
                        </select>
                    </div>
                    
                    <div id="failureReasonGroup" class="form-group" style="display: none;">
                        <label for="failure_reason">سبب الفشل</label>
                        <textarea id="failure_reason" placeholder="اكتب سبب فشل الصيانة..."></textarea>
                    </div>
                    
                    <div id="waitingPartsGroup" class="form-group" style="display: none;">
                        <label for="waiting_parts">قطع الغيار المطلوبة</label>
                        <textarea id="waiting_parts" placeholder="اكتب أسماء قطع الغيار المطلوبة..."></textarea>
                    </div>
                    
                    <div id="deliveryGroup" style="display: none;">
                        <div class="form-group">
                            <label for="receiver_name">اسم المستلم</label>
                            <input type="text" id="receiver_name" placeholder="اسم الشخص الذي استلم الجهاز">
                        </div>
                        
                        <div class="form-group">
                            <label for="delivery_date">تاريخ التسليم</label>
                            <input type="date" id="delivery_date">
                        </div>
                        
                        <div class="form-group">
                            <label for="payment_status">حالة الدفع</label>
                            <select id="payment_status" onchange="togglePaymentFields()">
                                <option value="">اختر حالة الدفع</option>
                                <option value="paid">تم الدفع كاملاً</option>
                                <option value="partial">دفع جزئي</option>
                                <option value="unpaid">لم يتم الدفع</option>
                            </select>
                        </div>
                        
                        <div id="partialPaymentGroup" class="form-group" style="display: none;">
                            <label for="paid_amount">المبلغ المدفوع</label>
                            <input type="number" id="paid_amount" step="0.01" min="0">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="status_notes">ملاحظات</label>
                        <textarea id="status_notes" placeholder="أي ملاحظات إضافية..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ التحديث
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeStatusModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="view_repairs.js"></script>
</body>
</html>
