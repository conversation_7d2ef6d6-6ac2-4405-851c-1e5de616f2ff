<?php
require_once '../config/init.php';
checkLogin();

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 15;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(full_name LIKE ? OR phone LIKE ? OR address LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد العملاء
$count_sql = "SELECT COUNT(*) as total FROM customers $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_customers = $count_stmt->fetch()['total'];
$total_pages = ceil($total_customers / $per_page);

// الحصول على العملاء
$sql = "SELECT c.*, 
        (SELECT COUNT(*) FROM repair_orders WHERE customer_id = c.id) as total_orders,
        (SELECT COUNT(*) FROM repair_orders WHERE customer_id = c.id AND status = 'completed') as completed_orders
        FROM customers c 
        $where_clause 
        ORDER BY c.created_at DESC 
        LIMIT $per_page OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$customers = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - إدارة العملاء</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="../repair/repair.css">
    <link rel="stylesheet" href="customers.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="../repair/add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="../repair/view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="customers.php" class="nav-item active">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-users"></i> إدارة العملاء</h1>
                <p>إدارة ومتابعة جميع عملاء المركز</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-primary" onclick="openAddCustomerModal()">
                    <i class="fas fa-user-plus"></i> إضافة عميل جديد
                </button>
            </div>
        </header>

        <!-- أدوات البحث -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <input type="text" name="search" placeholder="البحث في العملاء..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="customers.php" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <div class="stat-item">
                <i class="fas fa-users"></i>
                <span>إجمالي العملاء: <?php echo $total_customers; ?></span>
            </div>
        </div>

        <!-- بطاقات العملاء -->
        <div class="customers-container">
            <?php if (empty($customers)): ?>
            <div class="no-data">
                <i class="fas fa-users"></i>
                <h3>لا يوجد عملاء</h3>
                <p>لم يتم العثور على أي عملاء تطابق معايير البحث</p>
                <button class="btn btn-primary" onclick="openAddCustomerModal()">
                    <i class="fas fa-user-plus"></i> إضافة عميل جديد
                </button>
            </div>
            <?php else: ?>
            <div class="customers-grid">
                <?php foreach ($customers as $customer): ?>
                <div class="customer-card" data-customer-id="<?php echo $customer['id']; ?>">
                    <div class="customer-header">
                        <div class="customer-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="customer-info">
                            <h3><?php echo htmlspecialchars($customer['full_name']); ?></h3>
                            <p><i class="fas fa-phone"></i> <?php echo htmlspecialchars($customer['phone']); ?></p>
                            <?php if ($customer['address']): ?>
                            <p><i class="fas fa-map-marker-alt"></i> <?php echo htmlspecialchars($customer['address']); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="customer-actions">
                            <button class="btn-action btn-view" onclick="viewCustomer(<?php echo $customer['id']; ?>)" title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn-action btn-edit" onclick="editCustomer(<?php echo $customer['id']; ?>)" title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-action btn-delete" onclick="deleteCustomer(<?php echo $customer['id']; ?>)" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="customer-stats">
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $customer['total_orders']; ?></span>
                            <span class="stat-label">إجمالي الطلبات</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number"><?php echo $customer['completed_orders']; ?></span>
                            <span class="stat-label">طلبات مكتملة</span>
                        </div>
                        <div class="stat-item debt-stat">
                            <span class="stat-number"><?php echo formatCurrency($customer['total_debt']); ?></span>
                            <span class="stat-label">إجمالي الديون</span>
                        </div>
                    </div>
                    
                    <?php if ($customer['total_debt'] > 0): ?>
                    <div class="debt-actions">
                        <button class="btn btn-warning btn-sm" onclick="payDebt(<?php echo $customer['id']; ?>)">
                            <i class="fas fa-money-bill-wave"></i> تسديد دين
                        </button>
                    </div>
                    <?php endif; ?>
                    
                    <div class="customer-footer">
                        <small>تاريخ التسجيل: <?php echo date('Y-m-d', strtotime($customer['created_at'])); ?></small>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- ترقيم الصفحات -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>" class="page-btn">
                    <i class="fas fa-chevron-right"></i> السابق
                </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>" 
                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                    <?php echo $i; ?>
                </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>" class="page-btn">
                    التالي <i class="fas fa-chevron-left"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- نافذة إضافة/تعديل عميل -->
    <div id="customerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle"><i class="fas fa-user-plus"></i> إضافة عميل جديد</h3>
                <button class="modal-close" onclick="closeCustomerModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="customerForm">
                <div class="modal-body">
                    <input type="hidden" id="customer_id">
                    
                    <div class="form-group">
                        <label for="customer_name">الاسم الثلاثي *</label>
                        <input type="text" id="customer_name" required placeholder="أدخل الاسم الثلاثي">
                    </div>
                    
                    <div class="form-group">
                        <label for="customer_phone">رقم الهاتف *</label>
                        <input type="tel" id="customer_phone" required placeholder="07xxxxxxxxx">
                    </div>
                    
                    <div class="form-group">
                        <label for="customer_address">العنوان</label>
                        <input type="text" id="customer_address" placeholder="العنوان (اختياري)">
                    </div>
                    
                    <div class="form-group">
                        <label for="customer_social">وسائل التواصل الاجتماعي</label>
                        <input type="text" id="customer_social" placeholder="Facebook, Instagram, إلخ (اختياري)">
                    </div>
                    
                    <div class="form-group">
                        <label for="customer_notes">ملاحظات</label>
                        <textarea id="customer_notes" placeholder="أي ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ العميل
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeCustomerModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة تسديد الدين -->
    <div id="debtModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-money-bill-wave"></i> تسديد دين العميل</h3>
                <button class="modal-close" onclick="closeDebtModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="debtForm">
                <div class="modal-body">
                    <input type="hidden" id="debt_customer_id">
                    
                    <div class="debt-info">
                        <h4 id="debt_customer_name"></h4>
                        <p>إجمالي الدين: <span id="total_debt_amount" class="debt-amount"></span></p>
                    </div>
                    
                    <div class="form-group">
                        <label for="payment_type">نوع التسديد</label>
                        <select id="payment_type" required onchange="togglePaymentAmount()">
                            <option value="">اختر نوع التسديد</option>
                            <option value="full">تسديد كامل</option>
                            <option value="partial">تسديد جزئي</option>
                        </select>
                    </div>
                    
                    <div class="form-group" id="partialAmountGroup" style="display: none;">
                        <label for="payment_amount">المبلغ المدفوع</label>
                        <input type="number" id="payment_amount" step="0.01" min="0" placeholder="0.00">
                    </div>
                    
                    <div class="form-group">
                        <label for="payment_notes">ملاحظات التسديد</label>
                        <textarea id="payment_notes" placeholder="أي ملاحظات حول التسديد"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-check"></i> تأكيد التسديد
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeDebtModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="customers.js"></script>
</body>
</html>
