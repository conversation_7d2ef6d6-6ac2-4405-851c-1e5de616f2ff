<?php
require_once '../config/init.php';
checkLogin();

// الحصول على الإحصائيات
$stats = getStatistics();

// معالجة البحث الشامل
$search_results = [];
if (isset($_GET['search']) && !empty($_GET['search'])) {
    $search_query = sanitize($_GET['search']);
    $search_results = globalSearch($search_query);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - لوحة التحكم</title>
    <link rel="stylesheet" href="dashboard.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="dashboard.php" class="nav-item active">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="../repair/add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="../repair/view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <!-- الشريط العلوي -->
        <header class="top-header">
            <div class="header-left">
                <h1>مرحباً بك في لوحة التحكم</h1>
                <p>إدارة شاملة لمركز صيانة الموبايلات</p>
            </div>
            <div class="header-right">
                <div class="search-container">
                    <form method="GET" class="search-form">
                        <input type="text" name="search" placeholder="البحث الشامل في النظام..." 
                               value="<?php echo isset($_GET['search']) ? htmlspecialchars($_GET['search']) : ''; ?>">
                        <button type="submit">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span><?php echo $_SESSION['username']; ?></span>
                </div>
            </div>
        </header>

        <!-- نتائج البحث -->
        <?php if (!empty($search_results)): ?>
        <div class="search-results">
            <h3><i class="fas fa-search"></i> نتائج البحث عن: "<?php echo htmlspecialchars($_GET['search']); ?>"</h3>
            <div class="results-grid">
                <?php foreach ($search_results as $result): ?>
                <div class="result-card" onclick="openResult('<?php echo $result['type']; ?>', <?php echo $result['id']; ?>)">
                    <div class="result-icon">
                        <?php
                        switch($result['type']) {
                            case 'customer': echo '<i class="fas fa-user"></i>'; break;
                            case 'order': echo '<i class="fas fa-wrench"></i>'; break;
                            case 'part': echo '<i class="fas fa-cog"></i>'; break;
                        }
                        ?>
                    </div>
                    <div class="result-info">
                        <h4><?php echo htmlspecialchars($result['title']); ?></h4>
                        <p><?php echo htmlspecialchars($result['subtitle']); ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- بطاقات الإحصائيات -->
        <div class="stats-grid">
            <div class="stat-card customers">
                <div class="stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['customers']); ?></h3>
                    <p>إجمالي العملاء</p>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>

            <div class="stat-card orders">
                <div class="stat-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['orders']); ?></h3>
                    <p>إجمالي الطلبات</p>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>

            <div class="stat-card completed">
                <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['completed_orders']); ?></h3>
                    <p>طلبات مكتملة</p>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>

            <div class="stat-card parts">
                <div class="stat-icon">
                    <i class="fas fa-cogs"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['spare_parts']); ?></h3>
                    <p>قطع الغيار</p>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>

            <div class="stat-card sales">
                <div class="stat-icon">
                    <i class="fas fa-coins"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo formatCurrency($stats['monthly_sales']); ?></h3>
                    <p>مبيعات الشهر</p>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-up"></i>
                </div>
            </div>

            <div class="stat-card debts">
                <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo formatCurrency($stats['total_debt']); ?></h3>
                    <p>إجمالي الديون</p>
                </div>
                <div class="stat-trend">
                    <i class="fas fa-arrow-down"></i>
                </div>
            </div>
        </div>

        <!-- الإجراءات السريعة -->
        <div class="quick-actions">
            <h3><i class="fas fa-bolt"></i> الإجراءات السريعة</h3>
            <div class="actions-grid">
                <a href="../repair/add_repair.php" class="action-btn add-repair">
                    <i class="fas fa-plus-circle"></i>
                    <span>إضافة طلب صيانة جديد</span>
                </a>
                <a href="../customers/add_customer.php" class="action-btn add-customer">
                    <i class="fas fa-user-plus"></i>
                    <span>إضافة عميل جديد</span>
                </a>
                <a href="../parts/add_part.php" class="action-btn add-part">
                    <i class="fas fa-cog"></i>
                    <span>إضافة قطعة غيار</span>
                </a>
                <a href="../reports/daily_report.php" class="action-btn daily-report">
                    <i class="fas fa-chart-line"></i>
                    <span>تقرير اليوم</span>
                </a>
            </div>
        </div>
    </div>

    <script src="dashboard.js"></script>
</body>
</html>
