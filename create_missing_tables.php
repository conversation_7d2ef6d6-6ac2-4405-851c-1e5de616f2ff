<?php
// إنشاء الجداول المفقودة وإصلاح هيكل قاعدة البيانات

// إعداد الترميز
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
mb_regex_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إنشاء الجداول المفقودة</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; text-align: right; padding: 20px; background: #f5f5f5; }";
echo ".container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #e74c3c; background: #ffebee; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #3498db; background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".warning { color: #f39c12; background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo "h1 { color: #2c3e50; text-align: center; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo ".btn { background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إنشاء الجداول المفقودة وإصلاح هيكل قاعدة البيانات</h1>";

try {
    require_once 'config/database.php';
    
    $database = new Database();
    $conn = $database->connect();
    
    if (!$conn) {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    
    echo "<div class='info'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // إعداد الترميز
    $conn->exec("SET NAMES utf8mb4");
    $conn->exec("SET CHARACTER SET utf8mb4");
    $conn->exec("SET character_set_connection=utf8mb4");
    
    // الخطوة 1: إصلاح جدول الإعدادات
    echo "<div class='step'>";
    echo "<h2>الخطوة 1: إصلاح جدول الإعدادات</h2>";
    
    // التحقق من وجود الأعمدة في جدول settings
    $check_settings = "SHOW COLUMNS FROM settings LIKE 'setting_key'";
    $result = $conn->query($check_settings);
    
    if ($result->rowCount() == 0) {
        // إعادة إنشاء جدول الإعدادات
        $conn->exec("DROP TABLE IF EXISTS settings");
        $create_settings = "
        CREATE TABLE settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($create_settings);
        echo "<div class='success'>✅ تم إعادة إنشاء جدول الإعدادات</div>";
        
        // إدراج الإعدادات الافتراضية
        $settings = [
            'system_name' => 'نظام إدارة صيانة الموبايلات',
            'center_name' => 'مركز صيانة الموبايلات',
            'center_address' => 'العراق - بغداد',
            'center_phone' => '07xxxxxxxxx',
            'center_social' => 'Facebook: @center | Instagram: @center',
            'currency' => 'IQD',
            'print_size' => 'A5',
            'backup_frequency' => 'weekly',
            'auto_backup' => '1',
            'email_notifications' => '1',
            'sms_notifications' => '0'
        ];
        
        foreach ($settings as $key => $value) {
            $sql = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$key, $value]);
        }
        echo "<div class='success'>✅ تم إدراج الإعدادات الافتراضية</div>";
    } else {
        echo "<div class='info'>✅ جدول الإعدادات موجود ومُحدث</div>";
    }
    echo "</div>";
    
    // الخطوة 2: إنشاء جدول المستخدمين
    echo "<div class='step'>";
    echo "<h2>الخطوة 2: إنشاء جدول المستخدمين</h2>";
    
    $create_users = "
    CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        full_name VARCHAR(100),
        email VARCHAR(100),
        phone VARCHAR(20),
        role ENUM('admin', 'user') DEFAULT 'user',
        is_active BOOLEAN DEFAULT TRUE,
        last_login TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_users);
    echo "<div class='success'>✅ تم إنشاء جدول المستخدمين</div>";
    
    // إدراج المستخدم الافتراضي
    $check_user = "SELECT COUNT(*) as count FROM users WHERE username = 'abd'";
    $result = $conn->query($check_user);
    $user_exists = $result->fetch()['count'];
    
    if ($user_exists == 0) {
        $password_hash = password_hash('ZAin1998', PASSWORD_DEFAULT);
        $sql = "INSERT INTO users (username, password, full_name, role) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute(['abd', $password_hash, 'المدير العام', 'admin']);
        echo "<div class='success'>✅ تم إنشاء المستخدم الافتراضي (abd)</div>";
    } else {
        echo "<div class='info'>✅ المستخدم الافتراضي موجود</div>";
    }
    echo "</div>";
    
    // الخطوة 3: إنشاء جدول سجل العمليات
    echo "<div class='step'>";
    echo "<h2>الخطوة 3: إنشاء جدول سجل العمليات</h2>";
    
    $create_activity_logs = "
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NULL,
        action_type VARCHAR(100) NOT NULL,
        table_name VARCHAR(50) NOT NULL,
        record_id INT NULL,
        old_data JSON NULL,
        new_data JSON NULL,
        description TEXT,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
        INDEX idx_action_type (action_type),
        INDEX idx_table_name (table_name),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_activity_logs);
    echo "<div class='success'>✅ تم إنشاء جدول سجل العمليات</div>";
    echo "</div>";
    
    // الخطوة 4: إنشاء جدول التسلسل
    echo "<div class='step'>";
    echo "<h2>الخطوة 4: إنشاء جدول التسلسل</h2>";
    
    $create_sequences = "
    CREATE TABLE IF NOT EXISTS sequences (
        id INT AUTO_INCREMENT PRIMARY KEY,
        sequence_name VARCHAR(50) NOT NULL UNIQUE,
        current_value INT NOT NULL DEFAULT 0,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_sequences);
    echo "<div class='success'>✅ تم إنشاء جدول التسلسل</div>";
    
    // إدراج التسلسلات الافتراضية
    $sequences = ['repair_order', 'receipt', 'barcode'];
    foreach ($sequences as $seq) {
        $sql = "INSERT INTO sequences (sequence_name, current_value) VALUES (?, 0) 
                ON DUPLICATE KEY UPDATE sequence_name = VALUES(sequence_name)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$seq]);
    }
    echo "<div class='success'>✅ تم إدراج التسلسلات الافتراضية</div>";
    echo "</div>";
    
    // الخطوة 5: إنشاء جدول المستندات
    echo "<div class='step'>";
    echo "<h2>الخطوة 5: إنشاء جدول المستندات</h2>";
    
    $create_documents = "
    CREATE TABLE IF NOT EXISTS documents (
        id INT AUTO_INCREMENT PRIMARY KEY,
        document_type ENUM('repair_receipt', 'payment_receipt') NOT NULL,
        document_number VARCHAR(20) NOT NULL UNIQUE,
        related_id INT NOT NULL,
        customer_id INT NOT NULL,
        document_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
        INDEX idx_document_type (document_type),
        INDEX idx_document_number (document_number),
        INDEX idx_customer (customer_id),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($create_documents);
    echo "<div class='success'>✅ تم إنشاء جدول المستندات</div>";
    echo "</div>";
    
    // الخطوة 6: التحقق من جميع الجداول الأساسية
    echo "<div class='step'>";
    echo "<h2>الخطوة 6: التحقق من جميع الجداول</h2>";
    
    $required_tables = [
        'customers', 'repair_orders', 'spare_parts', 'phone_brands', 
        'payments', 'documents', 'activity_logs', 'settings', 'users', 'sequences'
    ];
    
    foreach ($required_tables as $table) {
        $check_table = "SHOW TABLES LIKE '$table'";
        $result = $conn->query($check_table);
        if ($result->rowCount() > 0) {
            echo "<div class='success'>✅ جدول $table موجود</div>";
        } else {
            echo "<div class='error'>❌ جدول $table مفقود</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 7: إنشاء الفهارس المفقودة
    echo "<div class='step'>";
    echo "<h2>الخطوة 7: إنشاء الفهارس المفقودة</h2>";
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_customers_debt ON customers(total_debt)",
        "CREATE INDEX IF NOT EXISTS idx_repair_orders_amounts ON repair_orders(repair_cost, paid_amount, remaining_amount)",
        "CREATE INDEX IF NOT EXISTS idx_spare_parts_prices ON spare_parts(purchase_price, selling_price)",
        "CREATE INDEX IF NOT EXISTS idx_payments_amount ON payments(amount)"
    ];
    
    foreach ($indexes as $index_sql) {
        try {
            $conn->exec($index_sql);
            echo "<div class='success'>✅ تم إنشاء فهرس</div>";
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ فهرس موجود مسبقاً أو خطأ: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🎉 تم إنشاء جميع الجداول المفقودة بنجاح!</h2>";
    echo "<div class='success'>";
    echo "<h3>الجداول المُنشأة:</h3>";
    echo "<ul>";
    echo "<li>✅ جدول المستخدمين (users)</li>";
    echo "<li>✅ جدول سجل العمليات (activity_logs)</li>";
    echo "<li>✅ جدول التسلسل (sequences)</li>";
    echo "<li>✅ جدول المستندات (documents)</li>";
    echo "<li>✅ جدول الإعدادات (settings) - مُحدث</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='info'>";
    echo "<h3>الآن يمكنك:</h3>";
    echo "<ul>";
    echo "<li>تشغيل ملف fix_all_encoding.php مرة أخرى</li>";
    echo "<li>استخدام جميع ميزات النظام</li>";
    echo "<li>تسجيل الدخول بالمستخدم: abd</li>";
    echo "<li>كلمة المرور: ZAin1998</li>";
    echo "</ul>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ حدث خطأ عام: " . $e->getMessage() . "</div>";
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='fix_all_encoding.php' class='btn'>تشغيل إصلاح الترميز مرة أخرى</a>";
echo "<a href='index.php' class='btn'>العودة للنظام</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
