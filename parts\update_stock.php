<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$part_id = (int)($input['part_id'] ?? 0);
$operation = sanitize($input['operation'] ?? '');
$quantity = (int)($input['quantity'] ?? 0);
$notes = sanitize($input['notes'] ?? '');

// التحقق من البيانات المطلوبة
if (!$part_id || !$operation || $quantity < 0) {
    echo json_encode(['success' => false, 'message' => 'جميع البيانات المطلوبة يجب أن تكون موجودة'], JSON_UNESCAPED_UNICODE);
    exit();
}

// التحقق من صحة نوع العملية
if (!in_array($operation, ['add', 'subtract', 'set'])) {
    echo json_encode(['success' => false, 'message' => 'نوع العملية غير صحيح'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // الحصول على بيانات القطعة الحالية
    $sql = "SELECT * FROM spare_parts WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$part_id]);
    $part = $stmt->fetch();
    
    if (!$part) {
        echo json_encode(['success' => false, 'message' => 'القطعة غير موجودة'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    $old_quantity = $part['quantity'];
    $new_quantity = $old_quantity;
    
    // حساب الكمية الجديدة حسب نوع العملية
    switch($operation) {
        case 'add':
            $new_quantity = $old_quantity + $quantity;
            break;
        case 'subtract':
            $new_quantity = max(0, $old_quantity - $quantity);
            break;
        case 'set':
            $new_quantity = $quantity;
            break;
    }
    
    // تحديث كمية القطعة
    $sql = "UPDATE spare_parts SET quantity = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$new_quantity, $part_id]);
    
    // تسجيل العملية في السجل
    $operation_text = '';
    switch($operation) {
        case 'add': $operation_text = "إضافة $quantity قطعة"; break;
        case 'subtract': $operation_text = "خصم $quantity قطعة"; break;
        case 'set': $operation_text = "تحديد الكمية إلى $quantity"; break;
    }
    
    $database->logActivity('تحديث مخزون', 'spare_parts', $part_id, 
        ['quantity' => $old_quantity], 
        ['quantity' => $new_quantity], 
        "تحديث مخزون {$part['part_name']}: $operation_text (من $old_quantity إلى $new_quantity)" . 
        ($notes ? " - $notes" : ""));
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديث المخزون بنجاح',
        'old_quantity' => $old_quantity,
        'new_quantity' => $new_quantity
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
