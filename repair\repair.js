// ملف JavaScript لنظام الصيانة

document.addEventListener('DOMContentLoaded', function() {
    initializeRepairForm();
    setupFormValidation();
    setupCustomerModal();
});

// تهيئة نموذج الصيانة
function initializeRepairForm() {
    const repairCostInput = document.getElementById('repair_cost');
    const partialAmountInput = document.getElementById('partial_amount');
    
    // تنسيق الأرقام أثناء الكتابة
    if (repairCostInput) {
        repairCostInput.addEventListener('input', function() {
            formatCurrencyInput(this);
            calculateRemainingAmount();
        });
    }
    
    if (partialAmountInput) {
        partialAmountInput.addEventListener('input', function() {
            formatCurrencyInput(this);
            calculateRemainingAmount();
        });
    }
    
    // تأثيرات بصرية للأقسام
    const formSections = document.querySelectorAll('.form-section');
    formSections.forEach((section, index) => {
        section.style.opacity = '0';
        section.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            section.style.transition = 'all 0.6s ease';
            section.style.opacity = '1';
            section.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// إظهار/إخفاء حقل الدفع الجزئي
function togglePartialPayment() {
    const paymentMethod = document.getElementById('payment_method').value;
    const partialGroup = document.getElementById('partialPaymentGroup');
    const partialInput = document.getElementById('partial_amount');
    
    if (paymentMethod === 'partial') {
        partialGroup.style.display = 'block';
        partialInput.required = true;
        
        // تأثير انتقالي
        partialGroup.style.opacity = '0';
        partialGroup.style.transform = 'translateY(-10px)';
        setTimeout(() => {
            partialGroup.style.transition = 'all 0.3s ease';
            partialGroup.style.opacity = '1';
            partialGroup.style.transform = 'translateY(0)';
        }, 50);
    } else {
        partialGroup.style.display = 'none';
        partialInput.required = false;
        partialInput.value = '';
    }
    
    calculateRemainingAmount();
}

// حساب المبلغ المتبقي
function calculateRemainingAmount() {
    const repairCost = parseFloat(document.getElementById('repair_cost').value) || 0;
    const paymentMethod = document.getElementById('payment_method').value;
    const partialAmount = parseFloat(document.getElementById('partial_amount').value) || 0;
    
    let remainingAmount = 0;
    
    if (paymentMethod === 'partial') {
        remainingAmount = repairCost - partialAmount;
    } else if (paymentMethod === 'after_repair') {
        remainingAmount = repairCost;
    }
    
    // عرض المبلغ المتبقي
    showRemainingAmount(remainingAmount);
}

// عرض المبلغ المتبقي
function showRemainingAmount(amount) {
    let remainingDisplay = document.getElementById('remainingAmountDisplay');
    
    if (!remainingDisplay) {
        remainingDisplay = document.createElement('div');
        remainingDisplay.id = 'remainingAmountDisplay';
        remainingDisplay.style.cssText = `
            margin-top: 10px;
            padding: 10px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 8px;
            border: 2px solid #3498db;
            font-weight: 600;
            color: #2c3e50;
            text-align: center;
        `;
        
        const paymentSection = document.querySelector('.form-section:nth-child(4)');
        if (paymentSection) {
            paymentSection.appendChild(remainingDisplay);
        }
    }
    
    if (amount > 0) {
        remainingDisplay.innerHTML = `
            <i class="fas fa-info-circle" style="color: #3498db; margin-left: 5px;"></i>
            المبلغ المتبقي: ${formatCurrency(amount)}
        `;
        remainingDisplay.style.display = 'block';
    } else {
        remainingDisplay.style.display = 'none';
    }
}

// تنسيق إدخال العملة
function formatCurrencyInput(input) {
    let value = input.value.replace(/[^\d.]/g, '');
    
    // التأكد من وجود نقطة عشرية واحدة فقط
    const parts = value.split('.');
    if (parts.length > 2) {
        value = parts[0] + '.' + parts.slice(1).join('');
    }
    
    input.value = value;
}

// تنسيق العملة للعرض
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: 'IQD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('IQD', 'دينار عراقي');
}

// إعداد التحقق من صحة النموذج
function setupFormValidation() {
    const form = document.getElementById('repairForm');
    
    form.addEventListener('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
        
        // إظهار مؤشر التحميل
        showSubmitLoading();
    });
}

// التحقق من صحة النموذج
function validateForm() {
    const errors = [];
    
    // التحقق من العميل
    const customerId = document.getElementById('customer_id').value;
    if (!customerId) {
        errors.push('يرجى اختيار العميل');
    }
    
    // التحقق من نوع الهاتف
    const phoneBrandId = document.getElementById('phone_brand_id').value;
    if (!phoneBrandId) {
        errors.push('يرجى اختيار نوع الهاتف');
    }
    
    // التحقق من موديل الهاتف
    const phoneModel = document.getElementById('phone_model').value.trim();
    if (!phoneModel) {
        errors.push('يرجى إدخال موديل الهاتف');
    }
    
    // التحقق من وصف المشكلة
    const problemDescription = document.getElementById('problem_description').value.trim();
    if (!problemDescription || problemDescription.length < 10) {
        errors.push('يرجى إدخال وصف مفصل للمشكلة (10 أحرف على الأقل)');
    }
    
    // التحقق من التكلفة
    const repairCost = parseFloat(document.getElementById('repair_cost').value);
    if (!repairCost || repairCost <= 0) {
        errors.push('يرجى إدخال تكلفة صحيحة للصيانة');
    }
    
    // التحقق من طريقة الدفع
    const paymentMethod = document.getElementById('payment_method').value;
    if (!paymentMethod) {
        errors.push('يرجى اختيار طريقة الدفع');
    }
    
    // التحقق من المبلغ الجزئي
    if (paymentMethod === 'partial') {
        const partialAmount = parseFloat(document.getElementById('partial_amount').value);
        if (!partialAmount || partialAmount <= 0) {
            errors.push('يرجى إدخال المبلغ المدفوع');
        } else if (partialAmount > repairCost) {
            errors.push('المبلغ المدفوع لا يمكن أن يكون أكبر من التكلفة الإجمالية');
        }
    }
    
    // عرض الأخطاء
    if (errors.length > 0) {
        showValidationErrors(errors);
        return false;
    }
    
    return true;
}

// عرض أخطاء التحقق
function showValidationErrors(errors) {
    // إزالة رسائل الخطأ السابقة
    const existingErrors = document.querySelectorAll('.validation-error');
    existingErrors.forEach(error => error.remove());
    
    // إنشاء رسالة خطأ جديدة
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-error validation-error';
    errorDiv.innerHTML = `
        <i class="fas fa-exclamation-triangle"></i>
        <div>
            <strong>يرجى تصحيح الأخطاء التالية:</strong>
            <ul style="margin: 5px 0 0 20px;">
                ${errors.map(error => `<li>${error}</li>`).join('')}
            </ul>
        </div>
    `;
    
    // إدراج رسالة الخطأ في بداية النموذج
    const formContainer = document.querySelector('.form-container');
    formContainer.insertBefore(errorDiv, formContainer.firstChild);
    
    // التمرير إلى رسالة الخطأ
    errorDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
    
    // إزالة رسالة الخطأ بعد 10 ثوان
    setTimeout(() => {
        if (errorDiv.parentNode) {
            errorDiv.remove();
        }
    }, 10000);
}

// إظهار مؤشر تحميل الإرسال
function showSubmitLoading() {
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;
    
    // استعادة النص الأصلي بعد 5 ثوان (في حالة عدم إعادة التوجيه)
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 5000);
}

// إعداد نافذة إضافة العميل
function setupCustomerModal() {
    const addCustomerForm = document.getElementById('addCustomerForm');
    
    if (addCustomerForm) {
        addCustomerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addNewCustomer();
        });
    }
}

// فتح نافذة إضافة العميل
function openAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    modal.classList.add('show');
    
    // التركيز على أول حقل
    setTimeout(() => {
        document.getElementById('new_customer_name').focus();
    }, 300);
}

// إغلاق نافذة إضافة العميل
function closeAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    modal.classList.remove('show');
    
    // مسح النموذج
    document.getElementById('addCustomerForm').reset();
}

// إضافة عميل جديد
function addNewCustomer() {
    const name = document.getElementById('new_customer_name').value.trim();
    const phone = document.getElementById('new_customer_phone').value.trim();
    const address = document.getElementById('new_customer_address').value.trim();
    
    if (!name || !phone) {
        alert('يرجى إدخال الاسم ورقم الهاتف');
        return;
    }
    
    // إظهار مؤشر التحميل
    const submitBtn = document.querySelector('#addCustomerForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;
    
    // إرسال البيانات
    fetch('../customers/add_customer_ajax.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            full_name: name,
            phone: phone,
            address: address
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // إضافة العميل إلى القائمة
            const customerSelect = document.getElementById('customer_id');
            const option = document.createElement('option');
            option.value = data.customer_id;
            option.textContent = `${name} - ${phone}`;
            option.selected = true;
            customerSelect.appendChild(option);
            
            // إغلاق النافذة
            closeAddCustomerModal();
            
            // إظهار رسالة نجاح
            showSuccessMessage('تم إضافة العميل بنجاح');
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success';
    successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    
    const formContainer = document.querySelector('.form-container');
    formContainer.insertBefore(successDiv, formContainer.firstChild);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.remove();
        }
    }, 5000);
}

// إغلاق النافذة عند النقر خارجها
document.addEventListener('click', function(e) {
    const modal = document.getElementById('addCustomerModal');
    if (e.target === modal) {
        closeAddCustomerModal();
    }
});

// إغلاق النافذة بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAddCustomerModal();
    }
});
