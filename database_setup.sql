-- إنشاء قاعدة البيانات مع دعم كامل للغة العربية
CREATE DATABASE IF NOT EXISTS irjnpfzw_seana 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE irjnpfzw_seana;

-- إ<PERSON><PERSON><PERSON><PERSON> جدول الإعدادات
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات الافتراضية
INSERT INTO settings (setting_key, setting_value) VALUES
('system_name', 'نظام إدارة صيانة الموبايلات'),
('center_name', 'مركز صيانة الموبايلات'),
('center_address', 'العراق - بغداد'),
('center_phone', '07xxxxxxxxx'),
('center_social', 'Facebook: @center | Instagram: @center'),
('currency', 'IQD'),
('print_size', 'A5'),
('backup_frequency', 'weekly'),
('auto_backup', '1'),
('email_notifications', '1'),
('sms_notifications', '0')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);

-- إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    role ENUM('admin', 'user') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج المستخدم الافتراضي
INSERT INTO users (username, password, full_name, role) VALUES
('abd', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'admin')
ON DUPLICATE KEY UPDATE password = VALUES(password);

-- إنشاء جدول العلامات التجارية للهواتف
CREATE TABLE IF NOT EXISTS phone_brands (
    id INT AUTO_INCREMENT PRIMARY KEY,
    brand_name VARCHAR(50) NOT NULL UNIQUE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج العلامات التجارية الشائعة
INSERT INTO phone_brands (brand_name) VALUES
('Samsung'), ('Apple'), ('Huawei'), ('Xiaomi'), ('Oppo'), 
('Vivo'), ('OnePlus'), ('Nokia'), ('LG'), ('Sony'), ('أخرى')
ON DUPLICATE KEY UPDATE brand_name = VALUES(brand_name);

-- إنشاء جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20) NOT NULL UNIQUE,
    address TEXT,
    social_media VARCHAR(200),
    notes TEXT,
    total_debt DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_phone (phone),
    INDEX idx_name (full_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول طلبات الصيانة
CREATE TABLE IF NOT EXISTS repair_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(20) NOT NULL UNIQUE,
    barcode VARCHAR(50) NOT NULL UNIQUE,
    customer_id INT NOT NULL,
    phone_brand_id INT NOT NULL,
    phone_model VARCHAR(100) NOT NULL,
    problem_description TEXT NOT NULL,
    repair_cost DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    remaining_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    payment_method ENUM('cash', 'card', 'transfer', 'partial') DEFAULT 'cash',
    status ENUM('pending', 'in_progress', 'completed', 'failed', 'waiting_parts', 'delivered') DEFAULT 'pending',
    failure_reason TEXT,
    waiting_parts TEXT,
    receiver_name VARCHAR(100),
    delivery_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (phone_brand_id) REFERENCES phone_brands(id),
    INDEX idx_order_number (order_number),
    INDEX idx_barcode (barcode),
    INDEX idx_customer (customer_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول قطع الغيار
CREATE TABLE IF NOT EXISTS spare_parts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    part_name VARCHAR(100) NOT NULL,
    barcode VARCHAR(50) UNIQUE,
    purchase_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    selling_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    quantity INT NOT NULL DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_part_name (part_name),
    INDEX idx_barcode (barcode),
    INDEX idx_quantity (quantity)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول المدفوعات
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    order_id INT NULL,
    payment_type ENUM('repair_payment', 'debt_payment') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'card', 'transfer') DEFAULT 'cash',
    receipt_number VARCHAR(20) NOT NULL UNIQUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (order_id) REFERENCES repair_orders(id) ON DELETE SET NULL,
    INDEX idx_customer (customer_id),
    INDEX idx_receipt (receipt_number),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول المستندات
CREATE TABLE IF NOT EXISTS documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    document_type ENUM('repair_receipt', 'payment_receipt') NOT NULL,
    document_number VARCHAR(20) NOT NULL UNIQUE,
    related_id INT NOT NULL,
    customer_id INT NOT NULL,
    document_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_document_type (document_type),
    INDEX idx_document_number (document_number),
    INDEX idx_customer (customer_id),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول سجل العمليات
CREATE TABLE IF NOT EXISTS activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action_type VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NULL,
    old_data JSON NULL,
    new_data JSON NULL,
    description TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_action_type (action_type),
    INDEX idx_table_name (table_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول التسلسل للأرقام
CREATE TABLE IF NOT EXISTS sequences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sequence_name VARCHAR(50) NOT NULL UNIQUE,
    current_value INT NOT NULL DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج التسلسلات الافتراضية
INSERT INTO sequences (sequence_name, current_value) VALUES
('repair_order', 0),
('receipt', 0),
('barcode', 0)
ON DUPLICATE KEY UPDATE sequence_name = VALUES(sequence_name);

-- إنشاء جدول النسخ الاحتياطية
CREATE TABLE IF NOT EXISTS backups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    backup_name VARCHAR(100) NOT NULL,
    backup_path VARCHAR(255),
    backup_size BIGINT,
    backup_type ENUM('manual', 'automatic') DEFAULT 'manual',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء مشغلات (Triggers) لتحديث ديون العملاء تلقائياً
DELIMITER //

CREATE TRIGGER update_customer_debt_after_order_insert
AFTER INSERT ON repair_orders
FOR EACH ROW
BEGIN
    UPDATE customers 
    SET total_debt = (
        SELECT COALESCE(SUM(remaining_amount), 0) 
        FROM repair_orders 
        WHERE customer_id = NEW.customer_id
    )
    WHERE id = NEW.customer_id;
END//

CREATE TRIGGER update_customer_debt_after_order_update
AFTER UPDATE ON repair_orders
FOR EACH ROW
BEGIN
    UPDATE customers 
    SET total_debt = (
        SELECT COALESCE(SUM(remaining_amount), 0) 
        FROM repair_orders 
        WHERE customer_id = NEW.customer_id
    )
    WHERE id = NEW.customer_id;
END//

CREATE TRIGGER update_customer_debt_after_order_delete
AFTER DELETE ON repair_orders
FOR EACH ROW
BEGIN
    UPDATE customers 
    SET total_debt = (
        SELECT COALESCE(SUM(remaining_amount), 0) 
        FROM repair_orders 
        WHERE customer_id = OLD.customer_id
    )
    WHERE id = OLD.customer_id;
END//

DELIMITER ;

-- إنشاء فهارس إضافية لتحسين الأداء
CREATE INDEX idx_customers_debt ON customers(total_debt);
CREATE INDEX idx_repair_orders_amounts ON repair_orders(repair_cost, paid_amount, remaining_amount);
CREATE INDEX idx_spare_parts_prices ON spare_parts(purchase_price, selling_price);
CREATE INDEX idx_payments_amount ON payments(amount);

-- إنشاء عرض (View) للإحصائيات السريعة
CREATE OR REPLACE VIEW dashboard_stats AS
SELECT 
    (SELECT COUNT(*) FROM customers) as total_customers,
    (SELECT COUNT(*) FROM repair_orders) as total_orders,
    (SELECT COUNT(*) FROM repair_orders WHERE status = 'completed') as completed_orders,
    (SELECT COUNT(*) FROM repair_orders WHERE status = 'pending') as pending_orders,
    (SELECT COUNT(*) FROM spare_parts) as total_parts,
    (SELECT COALESCE(SUM(repair_cost), 0) FROM repair_orders) as total_revenue,
    (SELECT COALESCE(SUM(paid_amount), 0) FROM repair_orders) as total_paid,
    (SELECT COALESCE(SUM(remaining_amount), 0) FROM repair_orders) as total_debt,
    (SELECT COUNT(*) FROM repair_orders WHERE DATE(created_at) = CURDATE()) as today_orders,
    (SELECT COALESCE(SUM(repair_cost), 0) FROM repair_orders WHERE DATE(created_at) = CURDATE()) as today_revenue;

-- تعيين الصلاحيات للمستخدم
GRANT ALL PRIVILEGES ON irjnpfzw_seana.* TO 'irjnpfzw_seana'@'localhost';
FLUSH PRIVILEGES;

-- إدراج بيانات تجريبية (اختيارية)
INSERT INTO customers (full_name, phone, address) VALUES
('أحمد محمد علي', '07701234567', 'بغداد - الكرادة'),
('فاطمة حسن محمود', '07801234567', 'بغداد - الجادرية'),
('محمد عبدالله أحمد', '07901234567', 'بغداد - المنصور')
ON DUPLICATE KEY UPDATE full_name = VALUES(full_name);

-- إدراج قطع غيار تجريبية
INSERT INTO spare_parts (part_name, barcode, purchase_price, selling_price, quantity) VALUES
('شاشة Samsung Galaxy S21', 'PART000001', 150000, 200000, 5),
('بطارية iPhone 12', 'PART000002', 80000, 120000, 10),
('كاميرا خلفية Huawei P30', 'PART000003', 100000, 150000, 3)
ON DUPLICATE KEY UPDATE part_name = VALUES(part_name);

-- تحسين قاعدة البيانات
OPTIMIZE TABLE customers, repair_orders, spare_parts, payments, documents, activity_logs;

-- إنهاء الإعداد
SELECT 'تم إنشاء قاعدة البيانات بنجاح مع دعم كامل للغة العربية' AS status;
