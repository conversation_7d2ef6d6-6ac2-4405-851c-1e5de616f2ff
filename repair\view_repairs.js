// ملف JavaScript لعرض الطلبات

document.addEventListener('DOMContentLoaded', function() {
    initializeViewRepairs();
    setupTableInteractions();
});

// تهيئة صفحة عرض الطلبات
function initializeViewRepairs() {
    // تأثيرات بصرية للجدول
    animateTableRows();
    
    // إعداد البحث المباشر
    setupLiveSearch();
    
    // إعداد نافذة تحديث الحالة
    setupStatusModal();
}

// تحريك صفوف الجدول
function animateTableRows() {
    const rows = document.querySelectorAll('.orders-table tbody tr');
    
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateX(20px)';
        
        setTimeout(() => {
            row.style.transition = 'all 0.4s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateX(0)';
        }, index * 50);
    });
}

// إعداد التفاعل مع الجدول
function setupTableInteractions() {
    const rows = document.querySelectorAll('.orders-table tbody tr');
    
    rows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.01)';
            this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
            this.style.boxShadow = 'none';
        });
    });
}

// عرض تفاصيل الطلب
function viewOrder(orderId) {
    // تأثير انتقالي
    const button = event.currentTarget;
    button.style.transform = 'scale(0.9)';
    
    setTimeout(() => {
        window.location.href = `view_order_details.php?id=${orderId}`;
    }, 150);
}

// تعديل الطلب
function editOrder(orderId) {
    const button = event.currentTarget;
    button.style.transform = 'scale(0.9)';
    
    setTimeout(() => {
        window.location.href = `edit_repair.php?id=${orderId}`;
    }, 150);
}

// طباعة الإيصال
function printReceipt(orderId) {
    const button = event.currentTarget;
    button.style.transform = 'scale(0.9)';
    
    setTimeout(() => {
        window.open(`print_receipt.php?id=${orderId}`, '_blank', 'width=800,height=600');
        button.style.transform = 'scale(1)';
    }, 150);
}

// تحديث حالة الطلب
function updateStatus(orderId) {
    document.getElementById('status_order_id').value = orderId;
    document.getElementById('statusModal').classList.add('show');
    
    // إعادة تعيين النموذج
    document.getElementById('statusForm').reset();
    hideAllStatusFields();
    
    // التركيز على حقل الحالة
    setTimeout(() => {
        document.getElementById('new_status').focus();
    }, 300);
}

// حذف الطلب
function deleteOrder(orderId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        // إظهار مؤشر التحميل
        const button = event.currentTarget;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        // إرسال طلب الحذف
        fetch('delete_repair.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ order_id: orderId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إزالة الصف من الجدول مع تأثير بصري
                const row = button.closest('tr');
                row.style.transition = 'all 0.5s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    row.remove();
                    showSuccessMessage('تم حذف الطلب بنجاح');
                }, 500);
            } else {
                alert('حدث خطأ: ' + data.message);
                button.innerHTML = originalIcon;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
            button.innerHTML = originalIcon;
            button.disabled = false;
        });
    }
}

// إعداد نافذة تحديث الحالة
function setupStatusModal() {
    const statusForm = document.getElementById('statusForm');
    
    if (statusForm) {
        statusForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitStatusUpdate();
        });
    }
    
    // إعداد تاريخ التسليم الافتراضي
    const deliveryDateInput = document.getElementById('delivery_date');
    if (deliveryDateInput) {
        deliveryDateInput.value = new Date().toISOString().split('T')[0];
    }
}

// إظهار/إخفاء حقول الحالة
function toggleStatusFields() {
    const status = document.getElementById('new_status').value;
    
    hideAllStatusFields();
    
    switch(status) {
        case 'failed':
            document.getElementById('failureReasonGroup').style.display = 'block';
            break;
        case 'waiting_parts':
            document.getElementById('waitingPartsGroup').style.display = 'block';
            break;
        case 'delivered':
            document.getElementById('deliveryGroup').style.display = 'block';
            break;
    }
}

// إخفاء جميع حقول الحالة
function hideAllStatusFields() {
    document.getElementById('failureReasonGroup').style.display = 'none';
    document.getElementById('waitingPartsGroup').style.display = 'none';
    document.getElementById('deliveryGroup').style.display = 'none';
    document.getElementById('partialPaymentGroup').style.display = 'none';
}

// إظهار/إخفاء حقول الدفع
function togglePaymentFields() {
    const paymentStatus = document.getElementById('payment_status').value;
    const partialGroup = document.getElementById('partialPaymentGroup');
    
    if (paymentStatus === 'partial') {
        partialGroup.style.display = 'block';
    } else {
        partialGroup.style.display = 'none';
    }
}

// إرسال تحديث الحالة
function submitStatusUpdate() {
    const formData = {
        order_id: document.getElementById('status_order_id').value,
        status: document.getElementById('new_status').value,
        failure_reason: document.getElementById('failure_reason').value,
        waiting_parts: document.getElementById('waiting_parts').value,
        receiver_name: document.getElementById('receiver_name').value,
        delivery_date: document.getElementById('delivery_date').value,
        payment_status: document.getElementById('payment_status').value,
        paid_amount: document.getElementById('paid_amount').value,
        notes: document.getElementById('status_notes').value
    };
    
    // إظهار مؤشر التحميل
    const submitBtn = document.querySelector('#statusForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;
    
    // إرسال البيانات
    fetch('update_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeStatusModal();
            showSuccessMessage('تم تحديث حالة الطلب بنجاح');
            
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// إغلاق نافذة تحديث الحالة
function closeStatusModal() {
    document.getElementById('statusModal').classList.remove('show');
}

// إعداد البحث المباشر
function setupLiveSearch() {
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
    }
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success';
    successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    successDiv.style.position = 'fixed';
    successDiv.style.top = '20px';
    successDiv.style.right = '20px';
    successDiv.style.zIndex = '9999';
    successDiv.style.minWidth = '300px';
    
    document.body.appendChild(successDiv);
    
    // إزالة الرسالة بعد 5 ثوان
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.style.opacity = '0';
            successDiv.style.transform = 'translateX(100%)';
            setTimeout(() => successDiv.remove(), 300);
        }
    }, 5000);
}

// إغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    const statusModal = document.getElementById('statusModal');
    if (e.target === statusModal) {
        closeStatusModal();
    }
});

// إغلاق النوافذ بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeStatusModal();
    }
});

// تحسين تجربة المستخدم للأزرار
document.addEventListener('DOMContentLoaded', function() {
    const actionButtons = document.querySelectorAll('.btn-action');
    
    actionButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
        
        button.addEventListener('mousedown', function() {
            this.style.transform = 'scale(0.95)';
        });
        
        button.addEventListener('mouseup', function() {
            this.style.transform = 'scale(1.1)';
        });
    });
});
