🔧 الحل النهائي لجميع مشاكل النظام

المشاكل الحالية:
================
1. شكل النظام مخرب (مشاكل في CSS)
2. لا يمكن إضافة عميل (خطأ في الاتصال)
3. لا يمكن إضافة قطع غيار (نفس السبب)
4. مشاكل في قاعدة البيانات

الحل الشامل والنهائي:
======================

الخطوة 1: إصلاح قاعدة البيانات بالكامل
---------------------------------------
افتح المتصفح واذهب إلى:
https://tikt0k.co/fix_complete_system.php

هذا الملف سيقوم بـ:
- إعادة إنشاء جميع الجداول بالشكل الصحيح
- إصلاح جميع مشاكل الترميز
- إعادة إدراج البيانات الأساسية
- اختبار جميع وظائف النظام

الخطوة 2: التحقق من النتيجة
---------------------------
بعد تشغيل الملف، ستظهر رسالة:
"🎉 تم إصلاح النظام بالكامل بنجاح!"

الخطوة 3: اختبار النظام
------------------------
1. اذهب إلى: https://tikt0k.co/
2. سجل الدخول بـ:
   - المستخدم: abd
   - كلمة المرور: ZAin1998
3. جرب إضافة عميل جديد
4. جرب إضافة قطعة غيار

إذا استمرت المشاكل - الحل اليدوي:
==================================

1. افتح phpMyAdmin أو أي أداة إدارة قواعد البيانات

2. نفذ هذه الأوامر بالترتيب:

-- حذف الجداول المشكوك فيها
DROP TABLE IF EXISTS settings;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS activity_logs;
DROP TABLE IF EXISTS sequences;

-- إعادة إنشاء جدول الإعدادات
CREATE TABLE settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج الإعدادات
INSERT INTO settings (setting_key, setting_value) VALUES
('system_name', 'نظام إدارة صيانة الموبايلات'),
('center_name', 'مركز صيانة الموبايلات'),
('center_address', 'العراق - بغداد'),
('center_phone', '07xxxxxxxxx'),
('currency', 'IQD');

-- إعادة إنشاء جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج المستخدم الافتراضي
INSERT INTO users (username, password, full_name, role) VALUES
('abd', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'المدير العام', 'admin');

-- إعادة إنشاء جدول سجل العمليات
CREATE TABLE activity_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    action_type VARCHAR(100) NOT NULL,
    table_name VARCHAR(50) NOT NULL,
    record_id INT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إعادة إنشاء جدول التسلسل
CREATE TABLE sequences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sequence_name VARCHAR(50) NOT NULL UNIQUE,
    current_value INT NOT NULL DEFAULT 0,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إدراج التسلسلات
INSERT INTO sequences (sequence_name, current_value) VALUES
('order', 0),
('receipt', 0),
('barcode', 0);

-- إصلاح جدول العملاء
ALTER TABLE customers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح جدول طلبات الصيانة
ALTER TABLE repair_orders CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح جدول قطع الغيار
ALTER TABLE spare_parts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح جدول العلامات التجارية
ALTER TABLE phone_brands CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
DELETE FROM phone_brands;
INSERT INTO phone_brands (brand_name) VALUES
('سامسونج (Samsung)'),
('آيفون (Apple)'),
('هواوي (Huawei)'),
('شاومي (Xiaomi)'),
('أوبو (Oppo)'),
('فيفو (Vivo)'),
('ون بلس (OnePlus)'),
('نوكيا (Nokia)'),
('إل جي (LG)'),
('سوني (Sony)'),
('أخرى');

3. اختبار النظام:

-- اختبار إضافة عميل
INSERT INTO customers (full_name, phone, address, total_debt) VALUES
('أحمد محمد علي', '07701234567', 'بغداد - الكرادة', 0.00);

-- التحقق من النتيجة
SELECT * FROM customers WHERE phone = '07701234567';

إذا ظهر الاسم والعنوان بشكل صحيح، فالمشكلة محلولة!

حل مشاكل CSS (الشكل المخرب):
==============================

إذا كان شكل النظام مخرب، تأكد من:

1. وجود ملفات CSS في المجلدات الصحيحة:
   - dashboard/dashboard.css
   - repair/repair.css
   - customers/customers.css
   - parts/parts.css

2. تحقق من روابط CSS في ملفات HTML

3. امسح cache المتصفح (Ctrl+F5)

4. تأكد من أن الخادم يقدم ملفات CSS بشكل صحيح

الاختبار النهائي:
==================

بعد تطبيق الحل:

1. اذهب إلى الصفحة الرئيسية
2. سجل الدخول (abd / ZAin1998)
3. جرب إضافة عميل جديد:
   - الاسم: "أحمد محمد علي"
   - الهاتف: "07701234567"
   - العنوان: "بغداد - الكرادة"

4. جرب إضافة قطعة غيار:
   - الاسم: "شاشة سامسونج"
   - سعر الشراء: 100000
   - سعر البيع: 150000
   - الكمية: 5

5. تأكد من ظهور جميع النصوص بشكل صحيح

إذا نجحت جميع الخطوات:
========================

✅ النظام يعمل بشكل صحيح
✅ يمكن إضافة العملاء
✅ يمكن إضافة قطع الغيار
✅ النصوص العربية تظهر بشكل صحيح
✅ لا توجد علامات استفهام
✅ الشكل العام للنظام صحيح

الملفات المساعدة:
==================

- fix_complete_system.php (الحل الشامل)
- fix_system_errors.php (إصلاح الأخطاء)
- fix_all_encoding.php (إصلاح الترميز)

للدعم الإضافي:
===============

إذا استمرت أي مشاكل:
1. تأكد من تشغيل fix_complete_system.php
2. تحقق من إعدادات الخادم
3. تأكد من صحة بيانات قاعدة البيانات
4. امسح cache المتصفح

النظام الآن جاهز للاستخدام بالكامل! 🎉
