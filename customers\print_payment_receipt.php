<?php
require_once '../config/init.php';
checkLogin();

$document_id = (int)$_GET['id'];

// الحصول على تفاصيل الوصل
$sql = "SELECT d.*, c.full_name, c.phone, c.address 
        FROM documents d 
        JOIN customers c ON d.customer_id = c.id 
        WHERE d.id = ? AND d.document_type = 'payment_receipt'";
$stmt = $conn->prepare($sql);
$stmt->execute([$document_id]);
$document = $stmt->fetch();

if (!$document) {
    die('الوصل غير موجود');
}

$document_data = json_decode($document['document_data'], true);
$print_size = getSetting('print_size', 'A5');
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>وصل تسديد ديون - <?php echo $document_data['receipt_number']; ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: white;
            color: #000;
            line-height: 1.6;
            direction: rtl;
            text-align: right;
        }

        .receipt-container {
            <?php if ($print_size === 'A5'): ?>
            width: 148mm;
            min-height: 210mm;
            <?php elseif ($print_size === 'thermal'): ?>
            width: 48mm;
            min-height: auto;
            <?php else: ?>
            width: 210mm;
            min-height: 297mm;
            <?php endif; ?>
            margin: 0 auto;
            padding: 10mm;
            background: white;
            border: 1px solid #ddd;
        }

        .receipt-header {
            text-align: center;
            border-bottom: 3px solid #27ae60;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .center-logo {
            width: 70px;
            height: 70px;
            background: #27ae60;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 28px;
        }

        .center-name {
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .center-info {
            font-size: 13px;
            color: #666;
            margin-bottom: 4px;
        }

        .receipt-title {
            background: #27ae60;
            color: white;
            padding: 12px;
            margin: 20px 0;
            text-align: center;
            font-weight: 700;
            font-size: 18px;
            border-radius: 8px;
        }

        .receipt-number {
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            border-radius: 8px;
            font-weight: 700;
            font-size: 16px;
            display: inline-block;
            margin-bottom: 15px;
        }

        .customer-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 2px solid #e9ecef;
        }

        .section-title {
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 16px;
            border-bottom: 2px solid #27ae60;
            padding-bottom: 5px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 8px 0;
            border-bottom: 1px dotted #ccc;
        }

        .detail-label {
            font-weight: 600;
            color: #2c3e50;
        }

        .detail-value {
            color: #000;
            font-weight: 500;
        }

        .payment-section {
            background: #d5f4e6;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #27ae60;
            margin-bottom: 20px;
        }

        .payment-amount {
            text-align: center;
            margin: 15px 0;
        }

        .amount-label {
            font-size: 14px;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .amount-value {
            font-size: 24px;
            font-weight: 700;
            color: #27ae60;
            background: white;
            padding: 10px;
            border-radius: 8px;
            border: 2px solid #27ae60;
        }

        .debt-summary {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #f39c12;
            margin-bottom: 20px;
        }

        .debt-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .debt-old {
            color: #e74c3c;
        }

        .debt-new {
            color: #27ae60;
        }

        .barcode-section {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            border: 2px dashed #27ae60;
            border-radius: 8px;
        }

        .barcode {
            font-family: 'Courier New', monospace;
            font-size: 16px;
            font-weight: 700;
            letter-spacing: 3px;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .receipt-footer {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 3px solid #27ae60;
            font-size: 13px;
        }

        .thank-you {
            background: #d5f4e6;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            color: #27ae60;
            font-weight: 700;
            font-size: 16px;
        }

        .print-buttons {
            text-align: center;
            margin: 20px 0;
        }

        .print-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            margin: 0 8px;
            font-size: 14px;
        }

        .print-btn:hover {
            background: #229954;
        }

        .print-btn.secondary {
            background: #95a5a6;
        }

        .print-btn.secondary:hover {
            background: #7f8c8d;
        }

        /* إخفاء الأزرار عند الطباعة */
        @media print {
            .print-buttons {
                display: none !important;
            }
            
            .receipt-container {
                border: none !important;
                margin: 0 !important;
                padding: 5mm !important;
            }
            
            body {
                background: white !important;
            }
        }

        /* تنسيق للطابعة الحرارية */
        <?php if ($print_size === 'thermal'): ?>
        .receipt-container {
            font-size: 11px;
            padding: 3mm;
        }
        
        .center-logo {
            width: 40px;
            height: 40px;
            font-size: 16px;
        }
        
        .center-name {
            font-size: 14px;
        }
        
        .receipt-title {
            font-size: 12px;
            padding: 6px;
        }
        
        .amount-value {
            font-size: 18px;
        }
        <?php endif; ?>

        /* ضمان عرض النصوص العربية */
        * {
            font-family: 'Cairo', sans-serif !important;
            direction: rtl !important;
            text-align: right !important;
        }
    </style>
</head>
<body>
    <div class="print-buttons">
        <button class="print-btn" onclick="window.print()">
            <i class="fas fa-print"></i> طباعة الوصل
        </button>
        <button class="print-btn secondary" onclick="window.close()">
            إغلاق
        </button>
    </div>

    <div class="receipt-container">
        <!-- رأس الوصل -->
        <div class="receipt-header">
            <div class="center-logo">
                💰
            </div>
            <div class="center-name"><?php echo $system_settings['center_name']; ?></div>
            <div class="center-info"><?php echo $system_settings['center_address']; ?></div>
            <div class="center-info">هاتف: <?php echo $system_settings['center_phone']; ?></div>
            <div class="center-info"><?php echo $system_settings['center_social']; ?></div>
        </div>

        <!-- عنوان الوصل -->
        <div class="receipt-title">وصل تسديد ديون</div>

        <!-- رقم الوصل -->
        <div style="text-align: center;">
            <span class="receipt-number">رقم الوصل: <?php echo $document_data['receipt_number']; ?></span>
        </div>

        <!-- معلومات العميل -->
        <div class="customer-section">
            <div class="section-title">معلومات العميل</div>
            <div class="detail-row">
                <span class="detail-label">الاسم:</span>
                <span class="detail-value"><?php echo htmlspecialchars($document_data['customer_name']); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">الهاتف:</span>
                <span class="detail-value"><?php echo htmlspecialchars($document_data['customer_phone']); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">تاريخ التسديد:</span>
                <span class="detail-value"><?php echo date('Y-m-d H:i', strtotime($document_data['date'])); ?></span>
            </div>
        </div>

        <!-- مبلغ التسديد -->
        <div class="payment-section">
            <div class="section-title">تفاصيل التسديد</div>
            <div class="payment-amount">
                <div class="amount-label">المبلغ المدفوع</div>
                <div class="amount-value"><?php echo formatCurrency($document_data['payment_amount']); ?></div>
            </div>
            <div class="detail-row">
                <span class="detail-label">نوع التسديد:</span>
                <span class="detail-value">
                    <?php echo $document_data['payment_type'] === 'full' ? 'تسديد كامل' : 'تسديد جزئي'; ?>
                </span>
            </div>
        </div>

        <!-- ملخص الديون -->
        <div class="debt-summary">
            <div class="section-title">ملخص الديون</div>
            <div class="debt-row">
                <span>الدين السابق:</span>
                <span class="debt-old"><?php echo formatCurrency($document_data['old_debt']); ?></span>
            </div>
            <div class="debt-row">
                <span>المبلغ المدفوع:</span>
                <span><?php echo formatCurrency($document_data['payment_amount']); ?></span>
            </div>
            <div class="debt-row" style="border-top: 2px solid #f39c12; padding-top: 8px; margin-top: 8px;">
                <span>الدين المتبقي:</span>
                <span class="debt-new"><?php echo formatCurrency($document_data['new_debt']); ?></span>
            </div>
        </div>

        <!-- الملاحظات -->
        <?php if (!empty($document_data['notes'])): ?>
        <div class="customer-section">
            <div class="section-title">ملاحظات</div>
            <p><?php echo nl2br(htmlspecialchars($document_data['notes'])); ?></p>
        </div>
        <?php endif; ?>

        <!-- الباركود -->
        <div class="barcode-section">
            <div class="barcode"><?php echo generateBarcode($document_data['receipt_number']); ?></div>
            <div style="font-size: 11px; color: #666;">احتفظ بهذا الوصل كإثبات للتسديد</div>
        </div>

        <!-- تذييل الوصل -->
        <div class="receipt-footer">
            <div class="thank-you">
                شكراً لكم على التسديد - نقدر ثقتكم بنا
            </div>
            <div style="font-size: 11px; color: #666;">
                هذا الوصل صالح كإثبات رسمي للتسديد<br>
                تاريخ الإصدار: <?php echo date('Y-m-d H:i:s'); ?>
            </div>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة (اختياري)
        window.addEventListener('load', function() {
            setTimeout(function() {
                // window.print();
            }, 500);
        });
    </script>
</body>
</html>
