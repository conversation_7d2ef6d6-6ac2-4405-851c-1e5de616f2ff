<?php
// ملف إعدادات قاعدة البيانات
// يمكن تعديل هذه الإعدادات حسب الحاجة

class Database {
    private $host = 'localhost';
    private $db_name = 'irjnpfzw_seana';
    private $username = 'irjnpfzw_seana';
    private $password = 'irjnpfzw_seana';
    private $charset = 'utf8mb4';
    private $conn;

    public function connect() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4";
            $this->conn = new PDO($dsn, $this->username, $this->password, array(
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ));
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

            // ضمان دعم اللغة العربية بشكل كامل
            $this->conn->exec("SET NAMES utf8mb4");
            $this->conn->exec("SET CHARACTER SET utf8mb4");
            $this->conn->exec("SET character_set_connection=utf8mb4");
            $this->conn->exec("SET character_set_results=utf8mb4");
            $this->conn->exec("SET character_set_client=utf8mb4");
            $this->conn->exec("SET collation_connection=utf8mb4_unicode_ci");
            $this->conn->exec("SET collation_database=utf8mb4_unicode_ci");
            $this->conn->exec("SET collation_server=utf8mb4_unicode_ci");
        } catch(PDOException $e) {
            echo "خطأ في الاتصال: " . $e->getMessage();
        }
        return $this->conn;
    }

    // إنشاء جداول قاعدة البيانات
    public function createTables() {
        $sql = "
        -- جدول الإعدادات
        CREATE TABLE IF NOT EXISTS settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_name VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        -- جدول العملاء
        CREATE TABLE IF NOT EXISTS customers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            full_name VARCHAR(255) NOT NULL,
            phone VARCHAR(20) NOT NULL,
            address TEXT,
            social_media TEXT,
            notes TEXT,
            total_debt DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        -- جدول الشركات المصنعة للهواتف
        CREATE TABLE IF NOT EXISTS phone_brands (
            id INT AUTO_INCREMENT PRIMARY KEY,
            brand_name VARCHAR(100) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- جدول طلبات الصيانة
        CREATE TABLE IF NOT EXISTS repair_orders (
            id INT AUTO_INCREMENT PRIMARY KEY,
            order_number VARCHAR(20) NOT NULL UNIQUE,
            customer_id INT NOT NULL,
            phone_brand_id INT NOT NULL,
            phone_model VARCHAR(100),
            problem_description TEXT NOT NULL,
            repair_cost DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'partial', 'after_repair') NOT NULL,
            paid_amount DECIMAL(10,2) DEFAULT 0.00,
            remaining_amount DECIMAL(10,2) DEFAULT 0.00,
            status ENUM('pending', 'in_progress', 'completed', 'failed', 'waiting_parts', 'delivered') DEFAULT 'pending',
            status_color VARCHAR(20) DEFAULT 'orange',
            failure_reason TEXT,
            waiting_parts TEXT,
            delivery_date DATE,
            receiver_name VARCHAR(255),
            delivery_notes TEXT,
            notes TEXT,
            barcode VARCHAR(50),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
            FOREIGN KEY (phone_brand_id) REFERENCES phone_brands(id)
        );

        -- جدول قطع الغيار
        CREATE TABLE IF NOT EXISTS spare_parts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            part_name VARCHAR(255) NOT NULL,
            purchase_price DECIMAL(10,2) NOT NULL,
            selling_price DECIMAL(10,2) NOT NULL,
            quantity INT NOT NULL DEFAULT 0,
            barcode VARCHAR(50),
            notes TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        );

        -- جدول المستندات
        CREATE TABLE IF NOT EXISTS documents (
            id INT AUTO_INCREMENT PRIMARY KEY,
            document_type ENUM('repair_receipt', 'payment_receipt') NOT NULL,
            document_number VARCHAR(20) NOT NULL,
            related_id INT NOT NULL,
            customer_id INT NOT NULL,
            document_data JSON,
            file_path VARCHAR(500),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
        );

        -- جدول المدفوعات
        CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            customer_id INT NOT NULL,
            order_id INT,
            payment_type ENUM('repair_payment', 'debt_payment') NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50),
            notes TEXT,
            receipt_number VARCHAR(20),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
            FOREIGN KEY (order_id) REFERENCES repair_orders(id) ON DELETE SET NULL
        );

        -- جدول السجل
        CREATE TABLE IF NOT EXISTS activity_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            action_type VARCHAR(100) NOT NULL,
            table_name VARCHAR(100),
            record_id INT,
            old_data JSON,
            new_data JSON,
            user_name VARCHAR(100),
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );

        -- إدراج البيانات الأساسية
        INSERT IGNORE INTO settings (setting_name, setting_value) VALUES
        ('system_name', 'نظام إدارة صيانة الموبايلات'),
        ('center_name', 'مركز صيانة الموبايلات'),
        ('center_address', 'العراق - بغداد'),
        ('center_phone', '07xxxxxxxxx'),
        ('center_social', 'Facebook: @center'),
        ('center_logo', ''),
        ('currency', 'دينار عراقي'),
        ('print_size', 'A5'),
        ('username', 'abd'),
        ('password', 'ZAin1998'),
        ('next_order_number', '1'),
        ('next_receipt_number', '1');

        INSERT IGNORE INTO phone_brands (brand_name) VALUES
        ('Samsung'), ('Apple'), ('Huawei'), ('Xiaomi'), ('Oppo'), 
        ('Vivo'), ('OnePlus'), ('Nokia'), ('LG'), ('Sony'), ('أخرى');
        ";

        try {
            $this->conn->exec($sql);
            return true;
        } catch(PDOException $e) {
            echo "خطأ في إنشاء الجداول: " . $e->getMessage();
            return false;
        }
    }

    // دالة لتسجيل العمليات في السجل
    public function logActivity($action_type, $table_name = null, $record_id = null, $old_data = null, $new_data = null, $description = null) {
        $sql = "INSERT INTO activity_log (action_type, table_name, record_id, old_data, new_data, user_name, description) 
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $action_type,
            $table_name,
            $record_id,
            $old_data ? json_encode($old_data, JSON_UNESCAPED_UNICODE) : null,
            $new_data ? json_encode($new_data, JSON_UNESCAPED_UNICODE) : null,
            'admin', // يمكن تطويرها لاحقاً لدعم عدة مستخدمين
            $description
        ]);
    }

    // دالة للحصول على الرقم التسلسلي التالي
    public function getNextSequenceNumber($type) {
        $setting_name = 'next_' . $type . '_number';
        $sql = "SELECT setting_value FROM settings WHERE setting_name = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$setting_name]);
        $result = $stmt->fetch();
        
        $next_number = $result ? (int)$result['setting_value'] : 1;
        
        // تحديث الرقم التالي
        $sql = "UPDATE settings SET setting_value = ? WHERE setting_name = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$next_number + 1, $setting_name]);
        
        return $next_number;
    }
}
?>
