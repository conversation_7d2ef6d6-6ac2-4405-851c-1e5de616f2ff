<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

$part_id = (int)($_GET['id'] ?? 0);

if (!$part_id) {
    echo json_encode(['success' => false, 'message' => 'معرف القطعة مطلوب'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    $sql = "SELECT * FROM spare_parts WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$part_id]);
    $part = $stmt->fetch();
    
    if (!$part) {
        echo json_encode(['success' => false, 'message' => 'القطعة غير موجودة'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    echo json_encode([
        'success' => true,
        'part' => $part
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
