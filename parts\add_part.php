<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$part_name = sanitize($input['part_name'] ?? '');
$purchase_price = (float)($input['purchase_price'] ?? 0);
$selling_price = (float)($input['selling_price'] ?? 0);
$quantity = (int)($input['quantity'] ?? 0);
$barcode = sanitize($input['barcode'] ?? '');
$notes = sanitize($input['notes'] ?? '');

// التحقق من البيانات المطلوبة
if (empty($part_name) || $purchase_price < 0 || $selling_price < 0 || $quantity < 0) {
    echo json_encode(['success' => false, 'message' => 'جميع البيانات المطلوبة يجب أن تكون صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

// إنشاء باركود تلقائي إذا لم يتم إدخاله
if (empty($barcode)) {
    $barcode = 'PART' . str_pad(rand(1, 999999), 6, '0', STR_PAD_LEFT);
}

// التحقق من عدم تكرار الباركود
$sql = "SELECT id FROM spare_parts WHERE barcode = ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$barcode]);
if ($stmt->fetch()) {
    echo json_encode(['success' => false, 'message' => 'الباركود موجود مسبقاً'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // إدراج القطعة الجديدة
    $sql = "INSERT INTO spare_parts (part_name, purchase_price, selling_price, quantity, barcode, notes) 
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$part_name, $purchase_price, $selling_price, $quantity, $barcode, $notes]);
    
    $part_id = $conn->lastInsertId();
    
    // تسجيل العملية في السجل
    $database->logActivity('إضافة قطعة غيار', 'spare_parts', $part_id, null, [
        'part_name' => $part_name,
        'purchase_price' => $purchase_price,
        'selling_price' => $selling_price,
        'quantity' => $quantity,
        'barcode' => $barcode,
        'notes' => $notes
    ], "تم إضافة قطعة غيار جديدة: $part_name");
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم إضافة القطعة بنجاح',
        'part_id' => $part_id,
        'barcode' => $barcode
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
