<?php
require_once '../config/init.php';

// إذا كان المستخدم مسجل دخول بالفعل، توجيه إلى لوحة التحكم
if (isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true) {
    header('Location: ../dashboard/dashboard.php');
    exit();
}

$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = sanitize($_POST['password']);
    
    // التحقق من بيانات تسجيل الدخول
    $stored_username = getSetting('username', 'abd');
    $stored_password = getSetting('password', 'ZAin1998');
    
    if ($username === $stored_username && $password === $stored_password) {
        $_SESSION['logged_in'] = true;
        $_SESSION['username'] = $username;
        $_SESSION['login_time'] = time();
        
        // تسجيل عملية تسجيل الدخول
        $database->logActivity('تسجيل دخول', null, null, null, null, "تم تسجيل دخول المستخدم: $username");
        
        header('Location: ../dashboard/dashboard.php');
        exit();
    } else {
        $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - تسجيل الدخول</title>
    <link rel="stylesheet" href="login.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-background">
            <div class="shape shape1"></div>
            <div class="shape shape2"></div>
            <div class="shape shape3"></div>
        </div>
        
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h1><?php echo $system_settings['system_name']; ?></h1>
                <h2><?php echo $system_settings['center_name']; ?></h2>
                <p>مرحباً بك في نظام إدارة الصيانة</p>
            </div>
            
            <form method="POST" class="login-form" id="loginForm">
                <?php if ($error_message): ?>
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>
                
                <div class="input-group">
                    <div class="input-wrapper">
                        <i class="fas fa-user"></i>
                        <input type="text" name="username" id="username" placeholder="اسم المستخدم" required>
                    </div>
                </div>
                
                <div class="input-group">
                    <div class="input-wrapper">
                        <i class="fas fa-lock"></i>
                        <input type="password" name="password" id="password" placeholder="كلمة المرور" required>
                        <button type="button" class="toggle-password" onclick="togglePassword()">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>
            
            <div class="login-footer">
                <p><i class="fas fa-shield-alt"></i> نظام آمن ومحمي</p>
                <div class="contact-info">
                    <p><i class="fas fa-phone"></i> <?php echo $system_settings['center_phone']; ?></p>
                    <p><i class="fas fa-map-marker-alt"></i> <?php echo $system_settings['center_address']; ?></p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="login.js"></script>
</body>
</html>
