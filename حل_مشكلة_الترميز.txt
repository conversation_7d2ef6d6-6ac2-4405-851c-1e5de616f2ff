🔧 حل مشكلة علامات الاستفهام (???) في النصوص العربية

المشكلة: عند إدخال أسماء عربية أو نصوص عربية تظهر علامات استفهام بدلاً من النص العربي

الحل السريع:
==============

1. افتح المتصفح واذهب إلى:
   http://your-domain.com/fix_all_encoding.php

2. ستظهر لك صفحة إصلاح الترميز
3. انتظر حتى تكتمل جميع الخطوات
4. ستظهر رسالة "تم إصلاح جميع مشاكل الترميز بنجاح!"

الحل اليدوي (إذا لم يعمل الحل السريع):
========================================

1. افتح phpMyAdmin أو أي أداة إدارة قاعدة البيانات

2. نفذ الأوامر التالية:

-- تحويل قاعدة البيانات
ALTER DATABASE irjnpfzw_seana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحويل جدول العملاء
ALTER TABLE customers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحويل جدول طلبات الصيانة
ALTER TABLE repair_orders CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحويل جدول قطع الغيار
ALTER TABLE spare_parts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحويل جدول العلامات التجارية
ALTER TABLE phone_brands CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- تحويل باقي الجداول
ALTER TABLE payments CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE documents CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE activity_logs CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE settings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

3. إعادة إدراج العلامات التجارية بالعربية:

DELETE FROM phone_brands;

INSERT INTO phone_brands (brand_name) VALUES 
('سامسونج (Samsung)'),
('آيفون (Apple)'),
('هواوي (Huawei)'),
('شاومي (Xiaomi)'),
('أوبو (Oppo)'),
('فيفو (Vivo)'),
('ون بلس (OnePlus)'),
('نوكيا (Nokia)'),
('إل جي (LG)'),
('سوني (Sony)'),
('ريلمي (Realme)'),
('تكنو (Tecno)'),
('إنفينكس (Infinix)'),
('أخرى');

4. تحديث الإعدادات:

UPDATE settings SET setting_value = 'نظام إدارة صيانة الموبايلات' WHERE setting_key = 'system_name';
UPDATE settings SET setting_value = 'مركز صيانة الموبايلات' WHERE setting_key = 'center_name';
UPDATE settings SET setting_value = 'العراق - بغداد' WHERE setting_key = 'center_address';

اختبار الحل:
============

1. اذهب إلى صفحة إضافة عميل جديد
2. أدخل اسم عربي مثل: "أحمد محمد علي"
3. أدخل عنوان عربي مثل: "بغداد - الكرادة"
4. احفظ العميل
5. تأكد من ظهور الاسم والعنوان بشكل صحيح

إذا استمرت المشكلة:
==================

1. تأكد من أن المتصفح يدعم UTF-8
2. امسح cache المتصفح
3. تأكد من أن الخادم يدعم UTF-8
4. تحقق من إعدادات PHP:
   - default_charset = "UTF-8"
   - mbstring.internal_encoding = UTF-8

ملاحظات مهمة:
==============

- بعد تطبيق الحل، أي بيانات قديمة تحتوي على ??? يجب إعادة إدخالها
- النظام الآن يدعم العربية بشكل كامل
- جميع النصوص الجديدة ستظهر بشكل صحيح
- يمكن استخدام الأرقام العربية والإنجليزية

للدعم الفني:
=============

إذا استمرت المشكلة بعد تطبيق جميع الحلول:
1. تأكد من تشغيل ملف fix_all_encoding.php
2. تحقق من إعدادات قاعدة البيانات
3. تأكد من أن جميع الملفات محفوظة بترميز UTF-8

نصائح إضافية:
==============

1. استخدم محرر نصوص يدعم UTF-8 عند تعديل الملفات
2. تأكد من حفظ الملفات بترميز UTF-8 (بدون BOM)
3. عند رفع الملفات للخادم، تأكد من الحفاظ على الترميز
4. استخدم FTP client يدعم UTF-8

الملفات المحدثة:
================

- config/database.php (محدث لدعم UTF-8)
- config/init.php (محدث لدعم UTF-8)
- fix_all_encoding.php (ملف الإصلاح الشامل)
- جميع ملفات النظام تدعم العربية الآن

بعد تطبيق الحل ستتمكن من:
===========================

✅ إضافة أسماء عربية كاملة للعملاء
✅ كتابة أوصاف المشاكل بالعربية
✅ إضافة أسماء قطع الغيار بالعربية  
✅ استخدام العناوين العربية
✅ كتابة الملاحظات بالعربية
✅ عرض جميع النصوص بشكل صحيح
✅ طباعة الإيصالات بالعربية

انتهى - النظام جاهز للاستخدام بالعربية الكاملة!
