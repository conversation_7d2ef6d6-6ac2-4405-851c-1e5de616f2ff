<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$customer_id = (int)($input['customer_id'] ?? 0);

if (!$customer_id) {
    echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // الحصول على بيانات العميل قبل الحذف
    $sql = "SELECT * FROM customers WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        echo json_encode(['success' => false, 'message' => 'العميل غير موجود'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    // التحقق من وجود طلبات صيانة للعميل
    $sql = "SELECT COUNT(*) as count FROM repair_orders WHERE customer_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $orders_count = $stmt->fetch()['count'];
    
    // بدء المعاملة
    $conn->beginTransaction();
    
    // حذف المدفوعات المرتبطة
    $sql = "DELETE FROM payments WHERE customer_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    
    // حذف المستندات المرتبطة
    $sql = "DELETE FROM documents WHERE customer_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    
    // حذف طلبات الصيانة المرتبطة
    $sql = "DELETE FROM repair_orders WHERE customer_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    
    // حذف العميل
    $sql = "DELETE FROM customers WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    
    // تأكيد المعاملة
    $conn->commit();
    
    // تسجيل العملية في السجل
    $database->logActivity('حذف عميل', 'customers', $customer_id, $customer, null, 
        "تم حذف العميل: {$customer['full_name']} مع $orders_count طلب صيانة");
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم حذف العميل وجميع البيانات المرتبطة به بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollBack();
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
