<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'طريقة الطلب غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    echo json_encode(['success' => false, 'message' => 'بيانات غير صحيحة'], JSON_UNESCAPED_UNICODE);
    exit();
}

$customer_id = (int)($input['id'] ?? 0);
$full_name = sanitize($input['full_name'] ?? '');
$phone = sanitize($input['phone'] ?? '');
$address = sanitize($input['address'] ?? '');
$social_media = sanitize($input['social_media'] ?? '');
$notes = sanitize($input['notes'] ?? '');

// التحقق من البيانات المطلوبة
if (!$customer_id || empty($full_name) || empty($phone)) {
    echo json_encode(['success' => false, 'message' => 'جميع البيانات المطلوبة يجب أن تكون موجودة'], JSON_UNESCAPED_UNICODE);
    exit();
}

// التحقق من صحة رقم الهاتف
if (strlen($phone) < 10) {
    echo json_encode(['success' => false, 'message' => 'رقم الهاتف غير صحيح'], JSON_UNESCAPED_UNICODE);
    exit();
}

// التحقق من عدم تكرار رقم الهاتف (باستثناء العميل الحالي)
$sql = "SELECT id FROM customers WHERE phone = ? AND id != ?";
$stmt = $conn->prepare($sql);
$stmt->execute([$phone, $customer_id]);
if ($stmt->fetch()) {
    echo json_encode(['success' => false, 'message' => 'رقم الهاتف موجود مسبقاً لعميل آخر'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // الحصول على البيانات القديمة للسجل
    $sql = "SELECT * FROM customers WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $old_data = $stmt->fetch();
    
    if (!$old_data) {
        echo json_encode(['success' => false, 'message' => 'العميل غير موجود'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    // تحديث بيانات العميل
    $sql = "UPDATE customers SET full_name = ?, phone = ?, address = ?, social_media = ?, notes = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$full_name, $phone, $address, $social_media, $notes, $customer_id]);
    
    // تسجيل العملية في السجل
    $new_data = [
        'full_name' => $full_name,
        'phone' => $phone,
        'address' => $address,
        'social_media' => $social_media,
        'notes' => $notes
    ];
    
    $database->logActivity('تحديث عميل', 'customers', $customer_id, $old_data, $new_data, "تم تحديث بيانات العميل: $full_name");
    
    echo json_encode([
        'success' => true, 
        'message' => 'تم تحديث بيانات العميل بنجاح'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
