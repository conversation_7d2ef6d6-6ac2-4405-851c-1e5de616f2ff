* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    display: flex;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
    width: 70px;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.sidebar-header .logo {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.sidebar-header .logo i {
    font-size: 25px;
    color: white;
}

.sidebar-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-header h3 {
    opacity: 0;
}

.sidebar-toggle {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    padding: 8px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

.sidebar-nav {
    padding: 20px 0;
}

.nav-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
    position: relative;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-right-color: #3498db;
}

.nav-item.active {
    background: rgba(52, 152, 219, 0.2);
    color: white;
    border-right-color: #3498db;
}

.nav-item i {
    font-size: 18px;
    width: 25px;
    margin-left: 15px;
}

.nav-item span {
    transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-item span {
    opacity: 0;
}

.sidebar-footer {
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.logout-btn {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    background: rgba(231, 76, 60, 0.3);
    color: white;
}

.logout-btn i {
    margin-left: 10px;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    padding: 20px;
    transition: all 0.3s ease;
}

.sidebar.collapsed + .main-content {
    margin-right: 70px;
}

/* الشريط العلوي */
.top-header {
    background: white;
    padding: 20px 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    color: #2c3e50;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.header-left p {
    color: #7f8c8d;
    font-size: 14px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-container {
    position: relative;
}

.search-form {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.search-form input {
    border: none;
    outline: none;
    padding: 12px 20px;
    background: transparent;
    font-family: 'Cairo', sans-serif;
    font-size: 14px;
    width: 300px;
}

.search-form button {
    background: linear-gradient(135deg, #3498db, #2980b9);
    border: none;
    color: white;
    padding: 12px 15px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-form button:hover {
    transform: scale(1.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #2c3e50;
    font-weight: 600;
}

.user-info i {
    font-size: 24px;
    color: #3498db;
}

/* نتائج البحث */
.search-results {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.search-results h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 18px;
}

.search-results h3 i {
    color: #3498db;
    margin-left: 10px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.result-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.result-card:hover {
    background: #e9ecef;
    border-color: #3498db;
    transform: translateY(-2px);
}

.result-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
}

.result-info h4 {
    color: #2c3e50;
    font-size: 16px;
    margin-bottom: 5px;
}

.result-info p {
    color: #7f8c8d;
    font-size: 14px;
}

/* بطاقات الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #3498db, #2980b9);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.customers::before { background: linear-gradient(180deg, #3498db, #2980b9); }
.stat-card.orders::before { background: linear-gradient(180deg, #e67e22, #d35400); }
.stat-card.completed::before { background: linear-gradient(180deg, #27ae60, #229954); }
.stat-card.parts::before { background: linear-gradient(180deg, #9b59b6, #8e44ad); }
.stat-card.sales::before { background: linear-gradient(180deg, #f39c12, #e67e22); }
.stat-card.debts::before { background: linear-gradient(180deg, #e74c3c, #c0392b); }

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.stat-card.customers .stat-icon { background: linear-gradient(135deg, #3498db, #2980b9); }
.stat-card.orders .stat-icon { background: linear-gradient(135deg, #e67e22, #d35400); }
.stat-card.completed .stat-icon { background: linear-gradient(135deg, #27ae60, #229954); }
.stat-card.parts .stat-icon { background: linear-gradient(135deg, #9b59b6, #8e44ad); }
.stat-card.sales .stat-icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
.stat-card.debts .stat-icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }

.stat-info {
    flex: 1;
}

.stat-info h3 {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #7f8c8d;
    font-size: 14px;
}

.stat-trend {
    color: #27ae60;
    font-size: 18px;
}

/* الإجراءات السريعة */
.quick-actions {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.quick-actions h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
}

.quick-actions h3 i {
    color: #f39c12;
    margin-left: 10px;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 12px;
    text-decoration: none;
    color: #2c3e50;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #3498db;
}

.action-btn i {
    font-size: 24px;
    color: #3498db;
}

.action-btn span {
    font-weight: 600;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }
    
    .main-content {
        margin-right: 70px;
    }
    
    .top-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .search-form input {
        width: 200px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: 1fr;
    }
}
