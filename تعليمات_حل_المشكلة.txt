🔧 تعليمات حل مشكلة علامات الاستفهام (???) خطوة بخطوة

المشكلة: تظهر علامات استفهام بدلاً من النصوص العربية

الحل المضمون:
===============

الخطوة 1: إنشاء الجداول المفقودة
---------------------------------
1. افتح المتصفح واذهب إلى:
   http://your-domain.com/create_missing_tables.php

2. انتظر حتى تكتمل العملية
3. ستظهر رسالة "تم إنشاء جميع الجداول المفقودة بنجاح!"

الخطوة 2: إصلاح الترميز
------------------------
1. بعد إنشاء الجداول، اذهب إلى:
   http://your-domain.com/fix_all_encoding.php

2. انتظر حتى تكتمل جميع الخطوات
3. ستظهر رسالة "تم إصلاح جميع مشاكل الترميز بنجاح!"

الخطوة 3: اختبار النظام
-----------------------
1. اذهب إلى الصفحة الرئيسية:
   http://your-domain.com/

2. سجل الدخول بالبيانات:
   - المستخدم: abd
   - كلمة المرور: ZAin1998

3. اذهب إلى "إضافة صيانة"
4. جرب إضافة عميل جديد بالاسم: "أحمد محمد علي"
5. تأكد من ظهور الاسم بشكل صحيح

إذا استمرت المشكلة - الحل اليدوي:
==================================

1. افتح phpMyAdmin أو أي أداة إدارة قواعد البيانات

2. نفذ هذه الأوامر بالترتيب:

-- إصلاح قاعدة البيانات
ALTER DATABASE irjnpfzw_seana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح جدول العملاء
ALTER TABLE customers CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE customers MODIFY full_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE customers MODIFY address TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح جدول طلبات الصيانة
ALTER TABLE repair_orders CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE repair_orders MODIFY phone_model VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE repair_orders MODIFY problem_description TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح جدول قطع الغيار
ALTER TABLE spare_parts CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE spare_parts MODIFY part_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إصلاح جدول العلامات التجارية
ALTER TABLE phone_brands CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE phone_brands MODIFY brand_name VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

3. إعادة إدراج العلامات التجارية:

DELETE FROM phone_brands;
INSERT INTO phone_brands (brand_name) VALUES 
('سامسونج (Samsung)'),
('آيفون (Apple)'),
('هواوي (Huawei)'),
('شاومي (Xiaomi)'),
('أوبو (Oppo)'),
('فيفو (Vivo)'),
('ون بلس (OnePlus)'),
('نوكيا (Nokia)'),
('إل جي (LG)'),
('سوني (Sony)'),
('أخرى');

4. اختبار النظام:

-- إدراج عميل تجريبي
INSERT INTO customers (full_name, phone, address) VALUES 
('أحمد محمد علي الاختبار', '07701234567', 'بغداد - الكرادة');

-- التحقق من النتيجة
SELECT full_name, address FROM customers WHERE phone = '07701234567';

إذا ظهر الاسم والعنوان بشكل صحيح، فالمشكلة محلولة!

نصائح مهمة:
============

1. تأكد من أن المتصفح يدعم UTF-8
2. امسح cache المتصفح (Ctrl+F5)
3. تأكد من أن الخادم يدعم UTF-8
4. عند إدخال بيانات جديدة، استخدم النسخ واللصق للنصوص العربية

ملاحظات:
=========

- بعد تطبيق الحل، أي بيانات قديمة تحتوي على ??? يجب إعادة إدخالها
- النظام الآن يدعم العربية بشكل كامل
- جميع النصوص الجديدة ستظهر بشكل صحيح

للتأكد من نجاح الحل:
====================

1. أضف عميل جديد باسم عربي
2. أضف طلب صيانة بوصف عربي للمشكلة
3. أضف قطعة غيار باسم عربي
4. تأكد من ظهور جميع النصوص بشكل صحيح

إذا نجحت جميع الخطوات، فالنظام جاهز للاستخدام!

الملفات المساعدة:
==================

- create_missing_tables.php (إنشاء الجداول المفقودة)
- fix_all_encoding.php (إصلاح الترميز الشامل)
- حل_مشكلة_الترميز.txt (دليل مفصل)

بعد تطبيق الحل ستتمكن من:
===========================

✅ إضافة أسماء عربية كاملة للعملاء
✅ كتابة أوصاف المشاكل بالعربية
✅ إضافة أسماء قطع الغيار بالعربية  
✅ استخدام العناوين العربية
✅ كتابة الملاحظات بالعربية
✅ عرض جميع النصوص بشكل صحيح
✅ طباعة الإيصالات بالعربية

انتهى - النظام جاهز للاستخدام بالعربية الكاملة!
