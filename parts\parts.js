// ملف JavaScript لإدارة قطع الغيار

document.addEventListener('DOMContentLoaded', function() {
    initializeParts();
    setupPartsModals();
    animatePartsTable();
    highlightLowStock();
});

// تهيئة صفحة قطع الغيار
function initializeParts() {
    // إعداد حساب الربح التلقائي
    const purchasePrice = document.getElementById('purchase_price');
    const sellingPrice = document.getElementById('selling_price');
    
    if (purchasePrice && sellingPrice) {
        purchasePrice.addEventListener('input', calculateProfit);
        sellingPrice.addEventListener('input', calculateProfit);
    }
    
    // إعداد تحديث المخزون التلقائي
    const stockOperation = document.getElementById('stock_operation');
    const stockQuantity = document.getElementById('stock_quantity');
    
    if (stockOperation && stockQuantity) {
        stockOperation.addEventListener('change', calculateNewQuantity);
        stockQuantity.addEventListener('input', calculateNewQuantity);
    }
}

// تحريك جدول قطع الغيار
function animatePartsTable() {
    const rows = document.querySelectorAll('.parts-table tbody tr');
    
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateX(20px)';
        
        setTimeout(() => {
            row.style.transition = 'all 0.4s ease';
            row.style.opacity = '1';
            row.style.transform = 'translateX(0)';
        }, index * 50);
    });
}

// تمييز القطع منخفضة المخزون
function highlightLowStock() {
    const rows = document.querySelectorAll('.parts-table tbody tr');
    
    rows.forEach(row => {
        const quantityElement = row.querySelector('.quantity');
        if (quantityElement && quantityElement.classList.contains('quantity-low')) {
            row.classList.add('low-stock');
            
            // إضافة تأثير وميض للقطع النافدة
            const quantity = parseInt(quantityElement.textContent.replace(/[^\d]/g, ''));
            if (quantity === 0) {
                row.classList.add('out-of-stock');
            }
        }
    });
}

// إعداد النوافذ المنبثقة
function setupPartsModals() {
    const partForm = document.getElementById('partForm');
    const stockForm = document.getElementById('stockForm');
    const importForm = document.getElementById('importForm');
    
    if (partForm) {
        partForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitPartForm();
        });
    }
    
    if (stockForm) {
        stockForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitStockUpdate();
        });
    }
    
    if (importForm) {
        importForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitImportForm();
        });
    }
}

// فتح نافذة إضافة قطعة
function openAddPartModal() {
    document.getElementById('partModalTitle').innerHTML = '<i class="fas fa-plus"></i> إضافة قطعة غيار جديدة';
    document.getElementById('partForm').reset();
    document.getElementById('part_id').value = '';
    document.getElementById('profitPreview').style.display = 'none';
    document.getElementById('partModal').classList.add('show');
    
    setTimeout(() => {
        document.getElementById('part_name').focus();
    }, 300);
}

// عرض تفاصيل القطعة
function viewPart(partId) {
    window.location.href = `view_part.php?id=${partId}`;
}

// تعديل القطعة
function editPart(partId) {
    fetch(`get_part.php?id=${partId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const part = data.part;
                
                document.getElementById('partModalTitle').innerHTML = '<i class="fas fa-edit"></i> تعديل قطعة الغيار';
                document.getElementById('part_id').value = part.id;
                document.getElementById('part_name').value = part.part_name;
                document.getElementById('purchase_price').value = part.purchase_price;
                document.getElementById('selling_price').value = part.selling_price;
                document.getElementById('quantity').value = part.quantity;
                document.getElementById('part_barcode').value = part.barcode || '';
                document.getElementById('part_notes').value = part.notes || '';
                
                calculateProfit();
                document.getElementById('partModal').classList.add('show');
                
                setTimeout(() => {
                    document.getElementById('part_name').focus();
                }, 300);
            } else {
                alert('حدث خطأ في جلب بيانات القطعة');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// حذف القطعة
function deletePart(partId) {
    if (confirm('هل أنت متأكد من حذف هذه القطعة؟\nهذا الإجراء لا يمكن التراجع عنه.')) {
        const button = event.currentTarget;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        fetch('delete_part.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ part_id: partId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const row = button.closest('tr');
                row.style.transition = 'all 0.5s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(-100%)';
                
                setTimeout(() => {
                    row.remove();
                    showSuccessMessage('تم حذف القطعة بنجاح');
                }, 500);
            } else {
                alert('حدث خطأ: ' + data.message);
                button.innerHTML = originalIcon;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
            button.innerHTML = originalIcon;
            button.disabled = false;
        });
    }
}

// طباعة ملصق القطعة
function printPartLabel(partId) {
    window.open(`print_part_label.php?id=${partId}`, '_blank', 'width=400,height=300');
}

// تحديث المخزون
function updateStock(partId) {
    fetch(`get_part.php?id=${partId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const part = data.part;
                
                document.getElementById('stock_part_id').value = part.id;
                document.getElementById('stock_part_name').textContent = part.part_name;
                document.getElementById('current_quantity').textContent = part.quantity;
                
                document.getElementById('stockForm').reset();
                document.getElementById('stock_part_id').value = part.id;
                document.getElementById('newQuantityPreview').style.display = 'none';
                
                document.getElementById('stockModal').classList.add('show');
                
                setTimeout(() => {
                    document.getElementById('stock_operation').focus();
                }, 300);
            } else {
                alert('حدث خطأ في جلب بيانات القطعة');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// إغلاق نوافذ القطع
function closePartModal() {
    document.getElementById('partModal').classList.remove('show');
}

function closeStockModal() {
    document.getElementById('stockModal').classList.remove('show');
}

function closeImportModal() {
    document.getElementById('importModal').classList.remove('show');
}

// حساب الربح
function calculateProfit() {
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;
    
    if (purchasePrice > 0 && sellingPrice > 0) {
        const profit = sellingPrice - purchasePrice;
        const profitPercentage = (profit / purchasePrice) * 100;
        
        document.getElementById('profitAmount').textContent = formatCurrency(profit);
        document.getElementById('profitPercentage').textContent = `(${profitPercentage.toFixed(1)}%)`;
        
        const profitPreview = document.getElementById('profitPreview');
        profitPreview.style.display = 'block';
        
        // تغيير لون الربح حسب القيمة
        const profitInfo = document.querySelector('.profit-info');
        if (profit >= 0) {
            profitInfo.style.color = '#27ae60';
            profitPreview.style.borderColor = '#27ae60';
            profitPreview.style.background = 'linear-gradient(135deg, #e8f5e8, #c8e6c9)';
        } else {
            profitInfo.style.color = '#e74c3c';
            profitPreview.style.borderColor = '#e74c3c';
            profitPreview.style.background = 'linear-gradient(135deg, #ffebee, #ffcdd2)';
        }
    } else {
        document.getElementById('profitPreview').style.display = 'none';
    }
}

// حساب الكمية الجديدة
function calculateNewQuantity() {
    const currentQuantity = parseInt(document.getElementById('current_quantity').textContent) || 0;
    const operation = document.getElementById('stock_operation').value;
    const quantity = parseInt(document.getElementById('stock_quantity').value) || 0;
    
    let newQuantity = currentQuantity;
    
    switch(operation) {
        case 'add':
            newQuantity = currentQuantity + quantity;
            break;
        case 'subtract':
            newQuantity = Math.max(0, currentQuantity - quantity);
            break;
        case 'set':
            newQuantity = quantity;
            break;
    }
    
    if (operation && quantity >= 0) {
        document.getElementById('newQuantityValue').textContent = newQuantity;
        document.getElementById('newQuantityPreview').style.display = 'block';
        
        // تغيير لون الكمية الجديدة
        const newQuantityElement = document.getElementById('newQuantityValue');
        if (newQuantity <= 5) {
            newQuantityElement.style.color = '#e74c3c';
        } else if (newQuantity <= 10) {
            newQuantityElement.style.color = '#f39c12';
        } else {
            newQuantityElement.style.color = '#27ae60';
        }
    } else {
        document.getElementById('newQuantityPreview').style.display = 'none';
    }
}

// إرسال نموذج القطعة
function submitPartForm() {
    const formData = {
        id: document.getElementById('part_id').value,
        part_name: document.getElementById('part_name').value.trim(),
        purchase_price: parseFloat(document.getElementById('purchase_price').value),
        selling_price: parseFloat(document.getElementById('selling_price').value),
        quantity: parseInt(document.getElementById('quantity').value),
        barcode: document.getElementById('part_barcode').value.trim(),
        notes: document.getElementById('part_notes').value.trim()
    };
    
    // التحقق من البيانات
    if (!formData.part_name || formData.purchase_price < 0 || formData.selling_price < 0 || formData.quantity < 0) {
        alert('يرجى إدخال جميع البيانات المطلوبة بشكل صحيح');
        return;
    }
    
    const submitBtn = document.querySelector('#partForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;
    
    const url = formData.id ? 'update_part.php' : 'add_part.php';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closePartModal();
            showSuccessMessage(formData.id ? 'تم تحديث القطعة بنجاح' : 'تم إضافة القطعة بنجاح');
            
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// إرسال تحديث المخزون
function submitStockUpdate() {
    const formData = {
        part_id: document.getElementById('stock_part_id').value,
        operation: document.getElementById('stock_operation').value,
        quantity: parseInt(document.getElementById('stock_quantity').value),
        notes: document.getElementById('stock_notes').value.trim()
    };
    
    if (!formData.operation || formData.quantity < 0) {
        alert('يرجى إدخال جميع البيانات المطلوبة');
        return;
    }
    
    const submitBtn = document.querySelector('#stockForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التحديث...';
    submitBtn.disabled = true;
    
    fetch('update_stock.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeStockModal();
            showSuccessMessage('تم تحديث المخزون بنجاح');
            
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// فتح نافذة الاستيراد
function openImportModal() {
    document.getElementById('importModal').classList.add('show');
}

// إرسال نموذج الاستيراد
function submitImportForm() {
    const fileInput = document.getElementById('excel_file');
    const updateExisting = document.getElementById('update_existing').checked;
    
    if (!fileInput.files[0]) {
        alert('يرجى اختيار ملف Excel');
        return;
    }
    
    const formData = new FormData();
    formData.append('excel_file', fileInput.files[0]);
    formData.append('update_existing', updateExisting);
    
    const submitBtn = document.querySelector('#importForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';
    submitBtn.disabled = true;
    
    showLoadingOverlay('جاري استيراد البيانات...');
    
    fetch('import_parts.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingOverlay();
        
        if (data.success) {
            closeImportModal();
            showSuccessMessage(`تم استيراد ${data.imported_count} قطعة بنجاح`);
            
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        hideLoadingOverlay();
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// تصدير قطع الغيار
function exportParts() {
    showLoadingOverlay('جاري تصدير البيانات...');
    
    fetch('export_parts.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ format: 'excel' })
    })
    .then(response => response.blob())
    .then(blob => {
        hideLoadingOverlay();
        
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `spare_parts_${new Date().toISOString().split('T')[0]}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        showSuccessMessage('تم تصدير البيانات بنجاح');
    })
    .catch(error => {
        hideLoadingOverlay();
        console.error('خطأ:', error);
        alert('حدث خطأ في التصدير');
    });
}

// تحميل نموذج Excel
function downloadTemplate() {
    window.open('download_template.php', '_blank');
}

// إظهار شاشة التحميل
function showLoadingOverlay(message) {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay';
    overlay.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>${message}</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

// إخفاء شاشة التحميل
function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
        overlay.remove();
    }
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: 'IQD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('IQD', 'دينار عراقي');
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success';
    successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    successDiv.style.position = 'fixed';
    successDiv.style.top = '20px';
    successDiv.style.right = '20px';
    successDiv.style.zIndex = '9999';
    successDiv.style.minWidth = '300px';
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
        if (successDiv.parentNode) {
            successDiv.style.opacity = '0';
            successDiv.style.transform = 'translateX(100%)';
            setTimeout(() => successDiv.remove(), 300);
        }
    }, 4000);
}

// إغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    const partModal = document.getElementById('partModal');
    const stockModal = document.getElementById('stockModal');
    const importModal = document.getElementById('importModal');
    
    if (e.target === partModal) closePartModal();
    if (e.target === stockModal) closeStockModal();
    if (e.target === importModal) closeImportModal();
});

// إغلاق النوافذ بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePartModal();
        closeStockModal();
        closeImportModal();
    }
});
