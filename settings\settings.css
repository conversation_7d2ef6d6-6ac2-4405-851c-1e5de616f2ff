/* ملف CSS لإعدادات النظام */

/* إحصائيات النظام */
.system-stats {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.system-stats h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.system-stats .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

/* حاوية الإعدادات */
.settings-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

/* أقسام الإعدادات */
.settings-section {
    padding: 30px;
    border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section h3 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 2px solid #3498db;
}

/* مجموعات النماذج */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 14px;
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    outline: none;
}

/* مربعات الاختيار المخصصة */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    font-weight: 500;
    color: #2c3e50;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #fff;
    border: 2px solid #e1e5e9;
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #3498db;
    border-color: #3498db;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* أزرار الإجراءات */
.form-actions {
    padding: 25px 30px;
    background: #f8f9fa;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

/* إدارة قاعدة البيانات */
.database-management {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.database-management h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.database-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* معلومات النظام */
.system-info {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.system-info h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.info-item strong {
    color: #2c3e50;
    font-weight: 600;
}

.info-item span {
    color: #7f8c8d;
    font-weight: 500;
}

/* رسالة التحذير */
.warning-message {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #f39c12;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.warning-message i {
    color: #f39c12;
    font-size: 20px;
    margin-top: 2px;
}

.warning-message p {
    margin: 0;
    color: #856404;
    line-height: 1.5;
}

/* تحسينات للأزرار */
.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496, #117a8b);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
}

/* تأثيرات الحركة */
.settings-section {
    transition: all 0.3s ease;
}

.settings-section:hover {
    background: #f8f9fa;
}

.form-group {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .settings-section {
        padding: 20px;
    }
    
    .form-actions {
        flex-direction: column;
        padding: 20px;
    }
    
    .form-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .database-actions {
        flex-direction: column;
    }
    
    .database-actions .btn {
        width: 100%;
        justify-content: center;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* تحسينات للطباعة */
@media print {
    .header-actions,
    .form-actions,
    .database-actions {
        display: none !important;
    }
    
    .settings-container,
    .database-management,
    .system-info {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        break-inside: avoid;
    }
}

/* تأثيرات خاصة */
.settings-form {
    position: relative;
}

.settings-form::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #3498db, #2980b9);
    border-radius: 0 15px 15px 0;
}

/* تحسينات للنصوص العربية */
* {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

input, textarea, select {
    direction: rtl !important;
    text-align: right !important;
}

input::placeholder,
textarea::placeholder {
    direction: rtl !important;
    text-align: right !important;
    opacity: 0.7;
}

/* تحسينات إضافية للتصميم */
.settings-section h3 i {
    color: #3498db;
}

.form-group input[type="file"] {
    padding: 8px;
    background: #f8f9fa;
    border-style: dashed;
}

.form-group input[type="file"]:hover {
    background: #e9ecef;
    border-color: #3498db;
}

/* تأثيرات التحميل */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
}

.loading-spinner i {
    font-size: 30px;
    color: #3498db;
    margin-bottom: 15px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تحسينات للإشعارات */
.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.alert-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert i {
    font-size: 18px;
}
