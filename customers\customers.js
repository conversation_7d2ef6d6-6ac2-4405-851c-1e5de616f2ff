// ملف JavaScript لإدارة العملاء

document.addEventListener('DOMContentLoaded', function() {
    initializeCustomers();
    setupCustomerModals();
    animateCustomerCards();
});

// تهيئة صفحة العملاء
function initializeCustomers() {
    // إضافة تأثيرات للبطاقات
    const customerCards = document.querySelectorAll('.customer-card');
    customerCards.forEach(card => {
        // تحديد العملاء المديونين
        const debtAmount = card.querySelector('.debt-stat .stat-number');
        if (debtAmount && parseFloat(debtAmount.textContent.replace(/[^\d.]/g, '')) > 0) {
            card.setAttribute('data-has-debt', 'true');
        }
        
        // تأثيرات التفاعل
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// تحريك بطاقات العملاء
function animateCustomerCards() {
    const cards = document.querySelectorAll('.customer-card');
    
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// إعداد النوافذ المنبثقة
function setupCustomerModals() {
    const customerForm = document.getElementById('customerForm');
    const debtForm = document.getElementById('debtForm');
    
    if (customerForm) {
        customerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitCustomerForm();
        });
    }
    
    if (debtForm) {
        debtForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitDebtPayment();
        });
    }
}

// فتح نافذة إضافة عميل
function openAddCustomerModal() {
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-user-plus"></i> إضافة عميل جديد';
    document.getElementById('customerForm').reset();
    document.getElementById('customer_id').value = '';
    document.getElementById('customerModal').classList.add('show');
    
    setTimeout(() => {
        document.getElementById('customer_name').focus();
    }, 300);
}

// عرض تفاصيل العميل
function viewCustomer(customerId) {
    window.location.href = `view_customer.php?id=${customerId}`;
}

// تعديل العميل
function editCustomer(customerId) {
    // جلب بيانات العميل
    fetch(`get_customer.php?id=${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const customer = data.customer;
                
                document.getElementById('modalTitle').innerHTML = '<i class="fas fa-user-edit"></i> تعديل بيانات العميل';
                document.getElementById('customer_id').value = customer.id;
                document.getElementById('customer_name').value = customer.full_name;
                document.getElementById('customer_phone').value = customer.phone;
                document.getElementById('customer_address').value = customer.address || '';
                document.getElementById('customer_social').value = customer.social_media || '';
                document.getElementById('customer_notes').value = customer.notes || '';
                
                document.getElementById('customerModal').classList.add('show');
                
                setTimeout(() => {
                    document.getElementById('customer_name').focus();
                }, 300);
            } else {
                alert('حدث خطأ في جلب بيانات العميل');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// حذف العميل
function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟\nسيتم حذف جميع طلبات الصيانة المرتبطة به أيضاً.\nهذا الإجراء لا يمكن التراجع عنه.')) {
        const button = event.currentTarget;
        const originalIcon = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;
        
        fetch('delete_customer.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ customer_id: customerId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const card = button.closest('.customer-card');
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '0';
                card.style.transform = 'scale(0.8)';
                
                setTimeout(() => {
                    card.remove();
                    showSuccessMessage('تم حذف العميل بنجاح');
                }, 500);
            } else {
                alert('حدث خطأ: ' + data.message);
                button.innerHTML = originalIcon;
                button.disabled = false;
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
            button.innerHTML = originalIcon;
            button.disabled = false;
        });
    }
}

// تسديد دين العميل
function payDebt(customerId) {
    // جلب بيانات العميل والدين
    fetch(`get_customer_debt.php?id=${customerId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const customer = data.customer;
                
                document.getElementById('debt_customer_id').value = customer.id;
                document.getElementById('debt_customer_name').textContent = customer.full_name;
                document.getElementById('total_debt_amount').textContent = formatCurrency(customer.total_debt);
                
                document.getElementById('debtForm').reset();
                document.getElementById('debt_customer_id').value = customer.id;
                document.getElementById('partialAmountGroup').style.display = 'none';
                
                document.getElementById('debtModal').classList.add('show');
                
                setTimeout(() => {
                    document.getElementById('payment_type').focus();
                }, 300);
            } else {
                alert('حدث خطأ في جلب بيانات العميل');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            alert('حدث خطأ في الاتصال');
        });
}

// إغلاق نافذة العميل
function closeCustomerModal() {
    document.getElementById('customerModal').classList.remove('show');
}

// إغلاق نافذة تسديد الدين
function closeDebtModal() {
    document.getElementById('debtModal').classList.remove('show');
}

// إظهار/إخفاء حقل المبلغ الجزئي
function togglePaymentAmount() {
    const paymentType = document.getElementById('payment_type').value;
    const partialGroup = document.getElementById('partialAmountGroup');
    const paymentAmount = document.getElementById('payment_amount');
    
    if (paymentType === 'partial') {
        partialGroup.style.display = 'block';
        paymentAmount.required = true;
        
        setTimeout(() => {
            paymentAmount.focus();
        }, 100);
    } else {
        partialGroup.style.display = 'none';
        paymentAmount.required = false;
        paymentAmount.value = '';
    }
}

// إرسال نموذج العميل
function submitCustomerForm() {
    const formData = {
        id: document.getElementById('customer_id').value,
        full_name: document.getElementById('customer_name').value.trim(),
        phone: document.getElementById('customer_phone').value.trim(),
        address: document.getElementById('customer_address').value.trim(),
        social_media: document.getElementById('customer_social').value.trim(),
        notes: document.getElementById('customer_notes').value.trim()
    };
    
    // التحقق من البيانات
    if (!formData.full_name || !formData.phone) {
        alert('يرجى إدخال الاسم ورقم الهاتف');
        return;
    }
    
    if (formData.phone.length < 10) {
        alert('رقم الهاتف غير صحيح');
        return;
    }
    
    const submitBtn = document.querySelector('#customerForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
    submitBtn.disabled = true;
    
    const url = formData.id ? 'update_customer.php' : 'add_customer.php';
    
    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeCustomerModal();
            showSuccessMessage(formData.id ? 'تم تحديث بيانات العميل بنجاح' : 'تم إضافة العميل بنجاح');
            
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// إرسال تسديد الدين
function submitDebtPayment() {
    const customerId = document.getElementById('debt_customer_id').value;
    const paymentType = document.getElementById('payment_type').value;
    const paymentAmount = document.getElementById('payment_amount').value;
    const notes = document.getElementById('payment_notes').value.trim();
    
    if (!paymentType) {
        alert('يرجى اختيار نوع التسديد');
        return;
    }
    
    if (paymentType === 'partial' && (!paymentAmount || parseFloat(paymentAmount) <= 0)) {
        alert('يرجى إدخال مبلغ صحيح للتسديد الجزئي');
        return;
    }
    
    const submitBtn = document.querySelector('#debtForm button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري التسديد...';
    submitBtn.disabled = true;
    
    const formData = {
        customer_id: customerId,
        payment_type: paymentType,
        payment_amount: paymentAmount,
        notes: notes
    };
    
    fetch('pay_debt.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeDebtModal();
            showSuccessMessage('تم تسديد الدين بنجاح');
            
            // فتح وصل التسديد في نافذة جديدة
            if (data.receipt_id) {
                setTimeout(() => {
                    window.open(`print_payment_receipt.php?id=${data.receipt_id}`, '_blank');
                }, 1000);
            }
            
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            alert('حدث خطأ: ' + data.message);
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        alert('حدث خطأ في الاتصال');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ', {
        style: 'currency',
        currency: 'IQD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount).replace('IQD', 'دينار عراقي');
}

// إظهار رسالة نجاح
function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success';
    successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
    successDiv.style.position = 'fixed';
    successDiv.style.top = '20px';
    successDiv.style.right = '20px';
    successDiv.style.zIndex = '9999';
    successDiv.style.minWidth = '300px';
    successDiv.style.animation = 'slideInRight 0.5s ease-out';
    
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
        successDiv.style.animation = 'slideOutRight 0.5s ease-in';
        setTimeout(() => successDiv.remove(), 500);
    }, 4000);
}

// إغلاق النوافذ عند النقر خارجها
document.addEventListener('click', function(e) {
    const customerModal = document.getElementById('customerModal');
    const debtModal = document.getElementById('debtModal');
    
    if (e.target === customerModal) {
        closeCustomerModal();
    }
    
    if (e.target === debtModal) {
        closeDebtModal();
    }
});

// إغلاق النوافذ بمفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeCustomerModal();
        closeDebtModal();
    }
});

// تحسين تجربة المستخدم للبحث
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.querySelector('input[name="search"]');
    let searchTimeout;
    
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            
            searchTimeout = setTimeout(() => {
                if (this.value.length >= 2 || this.value.length === 0) {
                    this.form.submit();
                }
            }, 500);
        });
    }
});

// إضافة تأثيرات CSS للحركة
const style = document.createElement('style');
style.textContent = `
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}
`;
document.head.appendChild(style);
