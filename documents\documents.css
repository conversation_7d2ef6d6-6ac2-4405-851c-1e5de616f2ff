/* ملف CSS لإدارة المستندات */

/* إحصائيات المستندات */
.stat-card.documents .stat-icon { 
    background: linear-gradient(135deg, #3498db, #2980b9); 
}

.stat-card.repair-receipts .stat-icon { 
    background: linear-gradient(135deg, #e67e22, #d35400); 
}

.stat-card.payment-receipts .stat-icon { 
    background: linear-gradient(135deg, #27ae60, #229954); 
}

.stat-card.today .stat-icon { 
    background: linear-gradient(135deg, #9b59b6, #8e44ad); 
}

/* فلاتر البحث المتقدمة */
.filters-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

/* جدول المستندات */
.documents-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    background: white;
}

.documents-table thead {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.documents-table th {
    padding: 15px 12px;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
}

.documents-table td {
    padding: 15px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;
}

.documents-table tbody tr {
    transition: all 0.3s ease;
}

.documents-table tbody tr:hover {
    background: #f8f9fa;
    transform: scale(1.01);
}

/* رقم المستند */
.document-number {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2c3e50;
}

.document-number i {
    color: #3498db;
    font-size: 16px;
}

/* نوع المستند */
.document-type {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.type-repair {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #ef6c00;
    border: 1px solid #ff9800;
}

.type-payment {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    border: 1px solid #4caf50;
}

/* معلومات العميل */
.customer-info strong {
    color: #2c3e50;
    font-size: 14px;
}

.customer-info small {
    color: #7f8c8d;
    font-size: 12px;
}

/* تاريخ المستند */
.document-date {
    color: #2c3e50;
    font-weight: 500;
}

.document-date small {
    color: #7f8c8d;
    font-size: 12px;
}

/* حالة المستند */
.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.status-active {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    border: 1px solid #4caf50;
}

/* أزرار الإجراءات الخاصة */
.btn-download {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
}

.btn-email {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-download:hover,
.btn-email:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* النوافذ المنبثقة الكبيرة */
.modal-large {
    max-width: 90%;
    max-height: 90%;
    width: 1000px;
}

.modal-large .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

/* محتوى المستند */
#documentContent {
    background: white;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    min-height: 400px;
}

/* تأثيرات خاصة للمستندات */
.documents-table tbody tr {
    position: relative;
}

.documents-table tbody tr::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 4px;
    height: 100%;
    background: transparent;
    transition: all 0.3s ease;
}

.documents-table tbody tr:hover::before {
    background: linear-gradient(180deg, #3498db, #2980b9);
}

/* تمييز المستندات حسب النوع */
.documents-table tbody tr[data-type="repair_receipt"]::before {
    background: linear-gradient(180deg, #e67e22, #d35400);
}

.documents-table tbody tr[data-type="payment_receipt"]::before {
    background: linear-gradient(180deg, #27ae60, #229954);
}

/* تحسينات للطباعة */
@media print {
    .header-actions,
    .filters-container,
    .action-buttons,
    .pagination {
        display: none !important;
    }
    
    .documents-table {
        font-size: 10px;
    }
    
    .documents-table th,
    .documents-table td {
        padding: 4px;
        border: 1px solid #000 !important;
    }
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .filters-form {
        grid-template-columns: 1fr;
    }
    
    .documents-table {
        font-size: 12px;
    }
    
    .documents-table th,
    .documents-table td {
        padding: 8px 6px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }
    
    .btn-action {
        width: 100%;
        height: 30px;
        font-size: 12px;
    }
    
    .modal-large {
        width: 95%;
        max-width: none;
    }
}

/* تحسينات إضافية */
.document-number:hover {
    color: #3498db;
    cursor: pointer;
}

.customer-info:hover {
    background: #f8f9fa;
    padding: 5px;
    border-radius: 5px;
    cursor: pointer;
}

/* تأثيرات التحميل للمستندات */
.document-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    color: #7f8c8d;
}

.document-loading i {
    font-size: 30px;
    margin-bottom: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تنسيق خاص للنصوص العربية */
* {
    font-family: 'Cairo', sans-serif !important;
    direction: rtl !important;
    text-align: right !important;
}

input, textarea, select {
    direction: rtl !important;
    text-align: right !important;
}

input::placeholder,
textarea::placeholder {
    direction: rtl !important;
    text-align: right !important;
    opacity: 0.7;
}

/* تحسينات للأيقونات */
.documents-table i {
    margin-left: 5px;
}

.btn-action i {
    font-size: 14px;
}

/* تأثيرات خاصة للمستندات الحديثة */
.documents-table tbody tr.recent {
    background: linear-gradient(135deg, #e8f5e8, #f1f8e9);
    border-left: 4px solid #4caf50;
}

.documents-table tbody tr.recent .document-number {
    color: #2e7d32;
}

/* تنسيق خاص للتواريخ */
.document-date {
    font-family: 'Cairo', sans-serif !important;
    direction: ltr;
    text-align: left;
}

/* تحسينات للنوافذ المنبثقة */
.modal-content {
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* تحسينات للفلاتر */
.filters-container {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid #dee2e6;
}

.filter-group input:focus,
.filter-group select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: scale(1.02);
}
