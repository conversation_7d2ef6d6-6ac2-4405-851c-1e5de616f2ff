<?php
// ملف إصلاح ترميز قاعدة البيانات للغة العربية

// إعداد الترميز
ini_set('default_charset', 'UTF-8');
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
mb_regex_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config/database.php';

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>إصلاح ترميز قاعدة البيانات</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; text-align: right; padding: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }";
echo ".success { color: #27ae60; background: #d5f4e6; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".error { color: #e74c3c; background: #ffebee; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo ".info { color: #3498db; background: #e3f2fd; padding: 10px; border-radius: 5px; margin: 10px 0; }";
echo "h1 { color: #2c3e50; text-align: center; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo ".step { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔧 إصلاح ترميز قاعدة البيانات للغة العربية</h1>";

try {
    $database = new Database();
    $conn = $database->connect();
    
    if (!$conn) {
        throw new Exception("فشل في الاتصال بقاعدة البيانات");
    }
    
    echo "<div class='info'>✅ تم الاتصال بقاعدة البيانات بنجاح</div>";
    
    // الخطوة 1: تحويل قاعدة البيانات إلى UTF-8
    echo "<div class='step'>";
    echo "<h2>الخطوة 1: تحويل قاعدة البيانات إلى UTF-8</h2>";
    
    $sql = "ALTER DATABASE irjnpfzw_seana CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    $conn->exec($sql);
    echo "<div class='success'>✅ تم تحويل قاعدة البيانات إلى UTF-8</div>";
    echo "</div>";
    
    // الخطوة 2: تحويل الجداول
    echo "<div class='step'>";
    echo "<h2>الخطوة 2: تحويل جميع الجداول إلى UTF-8</h2>";
    
    $tables = ['customers', 'repair_orders', 'spare_parts', 'phone_brands', 'payments', 'documents', 'activity_logs', 'settings', 'users'];
    
    foreach ($tables as $table) {
        try {
            $sql = "ALTER TABLE $table CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
            $conn->exec($sql);
            echo "<div class='success'>✅ تم تحويل جدول $table</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في تحويل جدول $table: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 3: تحديث الأعمدة النصية
    echo "<div class='step'>";
    echo "<h2>الخطوة 3: تحديث الأعمدة النصية</h2>";
    
    $column_updates = [
        "ALTER TABLE customers MODIFY full_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY address TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY social_media VARCHAR(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE customers MODIFY notes TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY phone_model VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY problem_description TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY failure_reason TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY waiting_parts TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY receiver_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE repair_orders MODIFY notes TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE spare_parts MODIFY part_name VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE spare_parts MODIFY notes TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE phone_brands MODIFY brand_name VARCHAR(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE activity_logs MODIFY action_type VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE activity_logs MODIFY description TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE settings MODIFY setting_key VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci",
        "ALTER TABLE settings MODIFY setting_value TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"
    ];
    
    foreach ($column_updates as $sql) {
        try {
            $conn->exec($sql);
            echo "<div class='success'>✅ تم تحديث عمود</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ خطأ في تحديث عمود: " . $e->getMessage() . "</div>";
        }
    }
    echo "</div>";
    
    // الخطوة 4: إعادة إدراج البيانات الأساسية بالعربية
    echo "<div class='step'>";
    echo "<h2>الخطوة 4: إعادة إدراج البيانات الأساسية</h2>";
    
    // حذف البيانات القديمة وإعادة إدراجها
    $conn->exec("DELETE FROM phone_brands");
    $brands = [
        'سامسونج (Samsung)',
        'آيفون (Apple)',
        'هواوي (Huawei)', 
        'شاومي (Xiaomi)',
        'أوبو (Oppo)',
        'فيفو (Vivo)',
        'ون بلس (OnePlus)',
        'نوكيا (Nokia)',
        'إل جي (LG)',
        'سوني (Sony)',
        'أخرى'
    ];
    
    foreach ($brands as $brand) {
        $sql = "INSERT INTO phone_brands (brand_name) VALUES (?)";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$brand]);
    }
    echo "<div class='success'>✅ تم إعادة إدراج العلامات التجارية بالعربية</div>";
    
    // تحديث الإعدادات
    $settings = [
        'system_name' => 'نظام إدارة صيانة الموبايلات',
        'center_name' => 'مركز صيانة الموبايلات',
        'center_address' => 'العراق - بغداد',
        'center_phone' => '07xxxxxxxxx',
        'center_social' => 'Facebook: @center | Instagram: @center'
    ];
    
    foreach ($settings as $key => $value) {
        $sql = "UPDATE settings SET setting_value = ? WHERE setting_key = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$value, $key]);
    }
    echo "<div class='success'>✅ تم تحديث الإعدادات بالعربية</div>";
    echo "</div>";
    
    // الخطوة 5: اختبار الترميز
    echo "<div class='step'>";
    echo "<h2>الخطوة 5: اختبار الترميز</h2>";
    
    // إدراج عميل تجريبي
    $test_customer = "أحمد محمد علي الاختبار";
    $test_phone = "07701234567";
    $test_address = "بغداد - الكرادة - شارع الاختبار";
    
    $sql = "INSERT INTO customers (full_name, phone, address) VALUES (?, ?, ?) 
            ON DUPLICATE KEY UPDATE full_name = VALUES(full_name), address = VALUES(address)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$test_customer, $test_phone, $test_address]);
    
    // قراءة البيانات للتأكد
    $sql = "SELECT full_name, address FROM customers WHERE phone = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$test_phone]);
    $result = $stmt->fetch();
    
    if ($result) {
        echo "<div class='success'>✅ اختبار الترميز ناجح:</div>";
        echo "<div class='info'>الاسم: " . $result['full_name'] . "</div>";
        echo "<div class='info'>العنوان: " . $result['address'] . "</div>";
    } else {
        echo "<div class='error'>❌ فشل في اختبار الترميز</div>";
    }
    echo "</div>";
    
    echo "<div class='step'>";
    echo "<h2>🎉 تم إصلاح ترميز قاعدة البيانات بنجاح!</h2>";
    echo "<div class='success'>";
    echo "<p><strong>الآن يمكنك:</strong></p>";
    echo "<ul>";
    echo "<li>إضافة عملاء بأسماء عربية</li>";
    echo "<li>إدخال أوصاف المشاكل بالعربية</li>";
    echo "<li>إضافة قطع غيار بأسماء عربية</li>";
    echo "<li>جميع النصوص ستظهر بشكل صحيح</li>";
    echo "</ul>";
    echo "</div>";
    echo "<div class='info'>";
    echo "<p><strong>ملاحظة:</strong> إذا كانت لديك بيانات قديمة تظهر علامات استفهام، قم بتعديلها وإعادة حفظها.</p>";
    echo "</div>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ حدث خطأ: " . $e->getMessage() . "</div>";
}

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='index.php' style='background: #3498db; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;'>العودة للنظام</a>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
