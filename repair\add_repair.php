<?php
require_once '../config/init.php';
checkLogin();

$success_message = '';
$error_message = '';

// الحصول على العملاء
$sql = "SELECT id, full_name, phone FROM customers ORDER BY full_name";
$stmt = $conn->prepare($sql);
$stmt->execute();
$customers = $stmt->fetchAll();

// الحصول على الشركات المصنعة
$sql = "SELECT id, brand_name FROM phone_brands ORDER BY brand_name";
$stmt = $conn->prepare($sql);
$stmt->execute();
$brands = $stmt->fetchAll();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $customer_id = (int)$_POST['customer_id'];
        $phone_brand_id = (int)$_POST['phone_brand_id'];
        $phone_model = sanitize($_POST['phone_model']);
        $problem_description = sanitize($_POST['problem_description']);
        $repair_cost = (float)$_POST['repair_cost'];
        $payment_method = sanitize($_POST['payment_method']);
        $notes = sanitize($_POST['notes']);
        
        // حساب المبالغ حسب طريقة الدفع
        $paid_amount = 0;
        $remaining_amount = $repair_cost;
        
        if ($payment_method === 'cash') {
            $paid_amount = $repair_cost;
            $remaining_amount = 0;
        } elseif ($payment_method === 'partial') {
            $paid_amount = (float)$_POST['partial_amount'];
            $remaining_amount = $repair_cost - $paid_amount;
        }
        
        // الحصول على رقم الطلب التالي
        $order_number = 'ORD' . str_pad($database->getNextSequenceNumber('order'), 6, '0', STR_PAD_LEFT);
        
        // إنشاء باركود
        $barcode = generateBarcode($order_number);
        
        // إدراج الطلب
        $sql = "INSERT INTO repair_orders (order_number, customer_id, phone_brand_id, phone_model, 
                problem_description, repair_cost, payment_method, paid_amount, remaining_amount, 
                notes, barcode, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $order_number, $customer_id, $phone_brand_id, $phone_model,
            $problem_description, $repair_cost, $payment_method, $paid_amount,
            $remaining_amount, $notes, $barcode
        ]);
        
        $order_id = $conn->lastInsertId();
        
        // تحديث ديون العميل
        updateCustomerDebt($customer_id);
        
        // تسجيل العملية في السجل
        $database->logActivity('إضافة طلب صيانة', 'repair_orders', $order_id, null, [
            'order_number' => $order_number,
            'customer_id' => $customer_id,
            'repair_cost' => $repair_cost
        ], "تم إضافة طلب صيانة جديد رقم: $order_number");
        
        $success_message = "تم إضافة طلب الصيانة بنجاح - رقم الطلب: $order_number";
        
        // توجيه لطباعة الإيصال
        echo "<script>
                setTimeout(function() {
                    window.open('print_receipt.php?id=$order_id', '_blank');
                }, 1000);
              </script>";
        
    } catch (Exception $e) {
        $error_message = "حدث خطأ: " . $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - إضافة طلب صيانة</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="repair.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="add_repair.php" class="nav-item active">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="../logs/logs.php" class="nav-item">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-plus-circle"></i> إضافة طلب صيانة جديد</h1>
                <p>إدخال تفاصيل طلب الصيانة وإنشاء الإيصال</p>
            </div>
            <div class="header-actions">
                <a href="view_repairs.php" class="btn btn-secondary">
                    <i class="fas fa-list"></i> عرض الطلبات
                </a>
            </div>
        </header>

        <?php if ($success_message): ?>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <?php echo $success_message; ?>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i>
            <?php echo $error_message; ?>
        </div>
        <?php endif; ?>

        <div class="form-container">
            <form method="POST" class="repair-form" id="repairForm">
                <div class="form-grid">
                    <!-- معلومات العميل -->
                    <div class="form-section">
                        <h3><i class="fas fa-user"></i> معلومات العميل</h3>
                        
                        <div class="form-group">
                            <label for="customer_id">العميل</label>
                            <div class="customer-select-container">
                                <select name="customer_id" id="customer_id" required>
                                    <option value="">اختر العميل</option>
                                    <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>">
                                        <?php echo htmlspecialchars($customer['full_name'] . ' - ' . $customer['phone']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="button" class="btn-add-customer" onclick="openAddCustomerModal()">
                                    <i class="fas fa-plus"></i> إضافة عميل جديد
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الجهاز -->
                    <div class="form-section">
                        <h3><i class="fas fa-mobile-alt"></i> معلومات الجهاز</h3>
                        
                        <div class="form-group">
                            <label for="phone_brand_id">نوع الهاتف</label>
                            <select name="phone_brand_id" id="phone_brand_id" required>
                                <option value="">اختر الشركة المصنعة</option>
                                <?php foreach ($brands as $brand): ?>
                                <option value="<?php echo $brand['id']; ?>">
                                    <?php echo htmlspecialchars($brand['brand_name']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone_model">موديل الهاتف</label>
                            <input type="text" name="phone_model" id="phone_model" 
                                   placeholder="مثال: Galaxy S21, iPhone 13" required>
                        </div>
                    </div>

                    <!-- تفاصيل المشكلة -->
                    <div class="form-section full-width">
                        <h3><i class="fas fa-exclamation-circle"></i> تفاصيل المشكلة</h3>
                        
                        <div class="form-group">
                            <label for="problem_description">وصف المشكلة (العطل)</label>
                            <textarea name="problem_description" id="problem_description" 
                                      placeholder="اكتب وصف مفصل للمشكلة..." required></textarea>
                        </div>
                    </div>

                    <!-- معلومات التكلفة والدفع -->
                    <div class="form-section">
                        <h3><i class="fas fa-money-bill-wave"></i> التكلفة والدفع</h3>
                        
                        <div class="form-group">
                            <label for="repair_cost">تكلفة الصيانة (<?php echo $system_settings['currency']; ?>)</label>
                            <input type="number" name="repair_cost" id="repair_cost" 
                                   step="0.01" min="0" placeholder="0.00" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="payment_method">طريقة الدفع</label>
                            <select name="payment_method" id="payment_method" required onchange="togglePartialPayment()">
                                <option value="">اختر طريقة الدفع</option>
                                <option value="cash">نقداً (كامل)</option>
                                <option value="partial">جزء من المبلغ</option>
                                <option value="after_repair">آجل بعد الصيانة</option>
                            </select>
                        </div>
                        
                        <div class="form-group" id="partialPaymentGroup" style="display: none;">
                            <label for="partial_amount">المبلغ المدفوع</label>
                            <input type="number" name="partial_amount" id="partial_amount" 
                                   step="0.01" min="0" placeholder="0.00">
                        </div>
                    </div>

                    <!-- ملاحظات إضافية -->
                    <div class="form-section">
                        <h3><i class="fas fa-sticky-note"></i> ملاحظات</h3>
                        
                        <div class="form-group">
                            <label for="notes">ملاحظات إضافية</label>
                            <textarea name="notes" id="notes" 
                                      placeholder="أي ملاحظات إضافية..."></textarea>
                        </div>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> إضافة طلب الصيانة
                    </button>
                    <button type="reset" class="btn btn-secondary">
                        <i class="fas fa-undo"></i> إعادة تعيين
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- نافذة إضافة عميل جديد -->
    <div id="addCustomerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> إضافة عميل جديد</h3>
                <button class="modal-close" onclick="closeAddCustomerModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addCustomerForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="new_customer_name">الاسم الثلاثي</label>
                        <input type="text" id="new_customer_name" required>
                    </div>
                    <div class="form-group">
                        <label for="new_customer_phone">رقم الهاتف</label>
                        <input type="tel" id="new_customer_phone" required>
                    </div>
                    <div class="form-group">
                        <label for="new_customer_address">العنوان</label>
                        <input type="text" id="new_customer_address">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> حفظ العميل
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeAddCustomerModal()">
                        إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="repair.js"></script>
</body>
</html>
