<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

$customer_id = (int)($_GET['id'] ?? 0);

if (!$customer_id) {
    echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    $sql = "SELECT * FROM customers WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        echo json_encode(['success' => false, 'message' => 'العميل غير موجود'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    echo json_encode([
        'success' => true,
        'customer' => $customer
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
