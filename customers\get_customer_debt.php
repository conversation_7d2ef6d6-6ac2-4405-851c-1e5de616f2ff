<?php
require_once '../config/init.php';
checkLogin();

header('Content-Type: application/json; charset=utf-8');

$customer_id = (int)($_GET['id'] ?? 0);

if (!$customer_id) {
    echo json_encode(['success' => false, 'message' => 'معرف العميل مطلوب'], JSON_UNESCAPED_UNICODE);
    exit();
}

try {
    // الحصول على بيانات العميل مع تحديث الديون
    updateCustomerDebt($customer_id);
    
    $sql = "SELECT id, full_name, phone, total_debt FROM customers WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        echo json_encode(['success' => false, 'message' => 'العميل غير موجود'], JSON_UNESCAPED_UNICODE);
        exit();
    }
    
    // الحصول على تفاصيل الطلبات المديونة
    $sql = "SELECT order_number, remaining_amount, created_at 
            FROM repair_orders 
            WHERE customer_id = ? AND remaining_amount > 0 
            ORDER BY created_at DESC";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$customer_id]);
    $debt_orders = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'customer' => $customer,
        'debt_orders' => $debt_orders
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'حدث خطأ: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
}
?>
