<?php
require_once '../config/init.php';
checkLogin();

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$action_type = isset($_GET['action_type']) ? sanitize($_GET['action_type']) : '';
$date_from = isset($_GET['date_from']) ? sanitize($_GET['date_from']) : '';
$date_to = isset($_GET['date_to']) ? sanitize($_GET['date_to']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 50;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(action_type LIKE ? OR table_name LIKE ? OR description LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term]);
}

if (!empty($action_type)) {
    $where_conditions[] = "action_type = ?";
    $params[] = $action_type;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(created_at) >= ?";
    $params[] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(created_at) <= ?";
    $params[] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// الحصول على إجمالي عدد السجلات
$count_sql = "SELECT COUNT(*) as total FROM activity_logs $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_logs = $count_stmt->fetch()['total'];
$total_pages = ceil($total_logs / $per_page);

// الحصول على السجلات
$sql = "SELECT * FROM activity_logs $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($params);
$logs = $stmt->fetchAll();

// الحصول على أنواع العمليات المختلفة
$actions_sql = "SELECT DISTINCT action_type FROM activity_logs ORDER BY action_type";
$actions_stmt = $conn->prepare($actions_sql);
$actions_stmt->execute();
$action_types = $actions_stmt->fetchAll();

// حساب إحصائيات سريعة
$stats_sql = "SELECT 
    COUNT(*) as total_activities,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_activities,
    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as week_activities,
    COUNT(DISTINCT action_type) as unique_actions
    FROM activity_logs";
$stats_stmt = $conn->prepare($stats_sql);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();

// دالة لتحديد لون العملية
function getActionColor($action) {
    switch($action) {
        case 'إضافة عميل':
        case 'إضافة طلب صيانة':
        case 'إضافة قطعة غيار':
            return '#27ae60';
        case 'تحديث عميل':
        case 'تحديث طلب صيانة':
        case 'تحديث قطعة غيار':
            return '#3498db';
        case 'حذف عميل':
        case 'حذف طلب صيانة':
        case 'حذف قطعة غيار':
            return '#e74c3c';
        case 'تسديد دين':
        case 'تحديث مخزون':
            return '#f39c12';
        case 'تسجيل دخول':
            return '#9b59b6';
        default:
            return '#95a5a6';
    }
}

// دالة لتحديد أيقونة العملية
function getActionIcon($action) {
    switch($action) {
        case 'إضافة عميل': return 'fas fa-user-plus';
        case 'تحديث عميل': return 'fas fa-user-edit';
        case 'حذف عميل': return 'fas fa-user-times';
        case 'إضافة طلب صيانة': return 'fas fa-plus-circle';
        case 'تحديث طلب صيانة': return 'fas fa-edit';
        case 'حذف طلب صيانة': return 'fas fa-trash';
        case 'إضافة قطعة غيار': return 'fas fa-cog';
        case 'تحديث قطعة غيار': return 'fas fa-cogs';
        case 'حذف قطعة غيار': return 'fas fa-times-circle';
        case 'تسديد دين': return 'fas fa-money-bill-wave';
        case 'تحديث مخزون': return 'fas fa-boxes';
        case 'تسجيل دخول': return 'fas fa-sign-in-alt';
        default: return 'fas fa-info-circle';
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $system_settings['system_name']; ?> - سجل العمليات</title>
    <link rel="stylesheet" href="../dashboard/dashboard.css">
    <link rel="stylesheet" href="../repair/repair.css">
    <link rel="stylesheet" href="logs.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <h3><?php echo $system_settings['center_name']; ?></h3>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <a href="../dashboard/dashboard.php" class="nav-item">
                <i class="fas fa-tachometer-alt"></i>
                <span>لوحة التحكم</span>
            </a>
            <a href="../repair/add_repair.php" class="nav-item">
                <i class="fas fa-plus-circle"></i>
                <span>إضافة صيانة</span>
            </a>
            <a href="../repair/view_repairs.php" class="nav-item">
                <i class="fas fa-list"></i>
                <span>عرض الطلبات</span>
            </a>
            <a href="../customers/customers.php" class="nav-item">
                <i class="fas fa-users"></i>
                <span>العملاء</span>
            </a>
            <a href="../parts/parts.php" class="nav-item">
                <i class="fas fa-cogs"></i>
                <span>قطع الغيار</span>
            </a>
            <a href="../documents/documents.php" class="nav-item">
                <i class="fas fa-file-alt"></i>
                <span>المستندات</span>
            </a>
            <a href="../reports/reports.php" class="nav-item">
                <i class="fas fa-chart-bar"></i>
                <span>التقارير</span>
            </a>
            <a href="logs.php" class="nav-item active">
                <i class="fas fa-history"></i>
                <span>السجل</span>
            </a>
            <a href="../settings/settings.php" class="nav-item">
                <i class="fas fa-cog"></i>
                <span>الإعدادات</span>
            </a>
        </nav>
        
        <div class="sidebar-footer">
            <a href="../login/logout.php" class="logout-btn">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </a>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <header class="page-header">
            <div class="header-content">
                <h1><i class="fas fa-history"></i> سجل العمليات</h1>
                <p>تتبع جميع العمليات والأنشطة في النظام</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-success" onclick="exportLogs()">
                    <i class="fas fa-download"></i> تصدير السجل
                </button>
                <button class="btn btn-danger" onclick="clearOldLogs()">
                    <i class="fas fa-trash"></i> مسح السجلات القديمة
                </button>
            </div>
        </header>

        <!-- أدوات البحث والفلترة -->
        <div class="filters-container">
            <form method="GET" class="filters-form">
                <div class="filter-group">
                    <label>البحث</label>
                    <input type="text" name="search" placeholder="البحث في السجل..." 
                           value="<?php echo htmlspecialchars($search); ?>">
                </div>
                
                <div class="filter-group">
                    <label>نوع العملية</label>
                    <select name="action_type">
                        <option value="">جميع العمليات</option>
                        <?php foreach ($action_types as $action): ?>
                        <option value="<?php echo htmlspecialchars($action['action_type']); ?>" 
                                <?php echo $action_type === $action['action_type'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($action['action_type']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="filter-group">
                    <label>من تاريخ</label>
                    <input type="date" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>">
                </div>
                
                <div class="filter-group">
                    <label>إلى تاريخ</label>
                    <input type="date" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>">
                </div>
                
                <div class="filter-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> بحث
                    </button>
                    <a href="logs.php" class="btn btn-secondary">
                        <i class="fas fa-refresh"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="stats-grid">
            <div class="stat-card total">
                <div class="stat-icon">
                    <i class="fas fa-list"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['total_activities']); ?></h3>
                    <p>إجمالي العمليات</p>
                </div>
            </div>
            
            <div class="stat-card today">
                <div class="stat-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['today_activities']); ?></h3>
                    <p>عمليات اليوم</p>
                </div>
            </div>
            
            <div class="stat-card week">
                <div class="stat-icon">
                    <i class="fas fa-calendar-week"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['week_activities']); ?></h3>
                    <p>عمليات الأسبوع</p>
                </div>
            </div>
            
            <div class="stat-card actions">
                <div class="stat-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="stat-info">
                    <h3><?php echo number_format($stats['unique_actions']); ?></h3>
                    <p>أنواع العمليات</p>
                </div>
            </div>
        </div>

        <!-- جدول السجل -->
        <div class="table-container">
            <?php if (empty($logs)): ?>
            <div class="no-data">
                <i class="fas fa-history"></i>
                <h3>لا توجد سجلات</h3>
                <p>لم يتم العثور على أي سجلات تطابق معايير البحث</p>
            </div>
            <?php else: ?>
            <div class="logs-timeline">
                <?php foreach ($logs as $log): ?>
                <div class="log-entry" data-action="<?php echo htmlspecialchars($log['action_type']); ?>">
                    <div class="log-icon" style="background-color: <?php echo getActionColor($log['action_type']); ?>">
                        <i class="<?php echo getActionIcon($log['action_type']); ?>"></i>
                    </div>
                    
                    <div class="log-content">
                        <div class="log-header">
                            <h4><?php echo htmlspecialchars($log['action_type']); ?></h4>
                            <span class="log-time"><?php echo date('Y-m-d H:i:s', strtotime($log['created_at'])); ?></span>
                        </div>
                        
                        <div class="log-description">
                            <?php echo htmlspecialchars($log['description']); ?>
                        </div>
                        
                        <div class="log-details">
                            <span class="log-table">الجدول: <?php echo htmlspecialchars($log['table_name']); ?></span>
                            <?php if ($log['record_id']): ?>
                            <span class="log-record">المعرف: <?php echo htmlspecialchars($log['record_id']); ?></span>
                            <?php endif; ?>
                        </div>
                        
                        <?php if ($log['old_data'] || $log['new_data']): ?>
                        <div class="log-actions">
                            <button class="btn-view-details" onclick="viewLogDetails(<?php echo $log['id']; ?>)">
                                <i class="fas fa-eye"></i> عرض التفاصيل
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>

            <!-- ترقيم الصفحات -->
            <?php if ($total_pages > 1): ?>
            <div class="pagination">
                <?php if ($page > 1): ?>
                <a href="?page=<?php echo $page - 1; ?>&search=<?php echo urlencode($search); ?>&action_type=<?php echo urlencode($action_type); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" class="page-btn">
                    <i class="fas fa-chevron-right"></i> السابق
                </a>
                <?php endif; ?>
                
                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&action_type=<?php echo urlencode($action_type); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" 
                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                    <?php echo $i; ?>
                </a>
                <?php endfor; ?>
                
                <?php if ($page < $total_pages): ?>
                <a href="?page=<?php echo $page + 1; ?>&search=<?php echo urlencode($search); ?>&action_type=<?php echo urlencode($action_type); ?>&date_from=<?php echo urlencode($date_from); ?>&date_to=<?php echo urlencode($date_to); ?>" class="page-btn">
                    التالي <i class="fas fa-chevron-left"></i>
                </a>
                <?php endif; ?>
            </div>
            <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- نافذة تفاصيل السجل -->
    <div id="logDetailsModal" class="modal">
        <div class="modal-content modal-large">
            <div class="modal-header">
                <h3><i class="fas fa-info-circle"></i> تفاصيل السجل</h3>
                <button class="modal-close" onclick="closeLogDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="logDetailsContent">
                    <!-- سيتم تحميل تفاصيل السجل هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeLogDetailsModal()">
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <script src="../dashboard/dashboard.js"></script>
    <script src="logs.js"></script>
</body>
</html>
